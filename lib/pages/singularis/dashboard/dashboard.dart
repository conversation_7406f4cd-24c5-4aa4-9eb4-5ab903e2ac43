import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:feature_discovery/feature_discovery.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:masterg/blocs/dashboard_content/dashboard_content_cubit.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/dashboard_content_resp.dart';
import 'package:masterg/data/models/response/auth_response/dashboard_view_resp.dart';
import 'package:masterg/data/models/response/home_response/course_category_list_id_response.dart';
import 'package:masterg/data/models/response/home_response/my_assessment_response.dart';
import 'package:masterg/data/models/response/home_response/my_assignment_response.dart';
import 'package:masterg/data/providers/mg_assessment_detail_provioder.dart';
import 'package:masterg/data/providers/my_assignment_detail_provider.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/main.dart';
import 'package:masterg/pages/custom_pages/ScreenWithLoader.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/rounded_appbar.dart';
import 'package:masterg/pages/ghome/my_courses.dart';
import 'package:masterg/pages/ghome/widget/view_widget_details_page.dart';
import 'package:masterg/pages/reels/reel_screen.dart';
import 'package:masterg/pages/singularis/competition/competition_detail.dart';
import 'package:masterg/pages/singularis/dashboard/industry_domain.dart';
import 'package:masterg/pages/singularis/dashboard/interest_area_page.dart';
import 'package:masterg/pages/singularis/dashboard/skill/skill_child_card_page.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/blank_page.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/build_your_portfolio_card.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/carvaan_list_item.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/competition_blank_page.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/hive_data_builder.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/set_goals_assessment_card.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/create_thumnail.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/future_trend_blank_page.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/future_trends_list.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/get_course_template.dart';
import 'package:masterg/pages/singularis/dashboard/widgets/job_blank_page.dart';
import 'package:masterg/pages/singularis/graph.dart';
import 'package:masterg/pages/singularis/job/job_details_page.dart';
import 'package:masterg/pages/singularis/wow_studio.dart';
import 'package:masterg/pages/training_pages/mg_assessment_detail.dart';
import 'package:masterg/pages/training_pages/mg_assignment_detail_page.dart';
import 'package:masterg/pages/training_pages/new_screen/assessment_your_report_page.dart';
import 'package:masterg/pages/user_profile_page/portfolio_create_form/portfolio_page.dart';
import 'package:masterg/routes/app_link_route.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/call_once.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/str_to_time.dart';
import 'package:masterg/utils/utility.dart';
import 'package:masterg/utils/video_screen.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shimmer/shimmer.dart';
import 'package:simple_barcode_scanner/simple_barcode_scanner.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';
import 'package:url_launcher/url_launcher.dart';
// import '../../../bots/set_goal_bot/bot_screen.dart';
// import '../../../bots/set_goal_bot/dialogFlow_Bot.dart';
import '../../../data/models/response/auth_response/bottombar_response.dart';
import '../../../data/models/response/auth_response/competition_my_activity.dart';
import '../../../data/models/response/home_response/competition_response.dart';
import '../../../data/models/response/home_response/gcarvaan_post_reponse.dart';
import '../../../data/models/response/home_response/portfolio_competition_response.dart';
import '../../../data/providers/training_detail_provider.dart';
import '../../../data/providers/video_player_provider.dart';
import '../../../utils/config.dart';
import '../../auth_pages/terms_and_condition_page.dart';
import '../../custom_pages/alert_widgets/alerts_widget.dart';
import '../../event/event_listing_page.dart';
import '../../explore_job/explore_job_list_page.dart' hide BlankPage;
import '../../gcarvaan/comment/comment_view_page.dart';
import '../../notifications/notification_list_page.dart';
import '../../training_pages/program_content/training_detail_page.dart';
import '../../training_pages/training_service.dart';
import '../app_drawer_page.dart';
import '../competition/competition_navigation/competition_my_activity.dart';
import '../job/job_dashboard_page.dart' hide BlankPage;

const String profileTour = 'profileTour';
const String menuTour = ' menuTour';

class DashboardPage extends StatefulWidget {
  final fromDashboard;

  const DashboardPage({Key? key, this.fromDashboard}) : super(key: key);

  @override
  _DashboardPageState createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage>
    with SingleTickerProviderStateMixin {
  var _scaffoldKey = GlobalKey<ScaffoldState>();
  Box? box;
  bool? dashboardIsVisibleLoading = true;
  bool? dasboardListLoading = true;
  bool? domainLoading = true;
  bool? featuredInternshipsLoading = true;
  bool? jobApplyLoading = true;

  DashboardContentResponse? dashboardContentResponse;
  List<MatchingJobs>? matchingJobsResp;
  List<DashboardFeaturedContentLimit>? featuredContentList;
  List<DashboardRecommendedCoursesLimit>? recommendedCourseList;
  List<DashboardReelsLimit>? reelsList;
  List<DashboardCarvanLimit>? carvaanList;
  List<DashboardSessionsLimit>? sessionList;
  List<DashboardMyCoursesLimit>? myCoursesList;
  List<RecentActivity>? recentActivity;
  DashboardViewResponse? dashboardViewResponse;
  bool showAllFeatured = false;
  MenuListProvider? menuProvider;
  late int selectedPage;
  late final PageController _pageController;
  CompetitionResponse? competitionResponse, featuredInternshipsResponse;
  PortfolioCompetitionResponse? completedCompetition;
  CompetitionMyActivityResponse? myActivity;
  // DomainListResponse? domainList;
  String? currentZoomUrl;
  String? currentOpenUrl;
  bool? isJoinClassLoading = false;
  double percent = .5;

  int _current = 0;
  final CarouselSliderController _controller = CarouselSliderController();
  List<Widget>? imageSliders;

  ///add new
  List<AssessmentList>? assessmentList = [];
  var isLoading = true;
  bool fromProfileCard = false;
  int? assessmentFirstIndex;
  int? applied;

  ///Animation
  late final AnimationController _controllerAnim;
  late final Animation<double> _animation;
  bool isEnableEvent =
      true; //  change value 04-02-2025  isEnableEvent = false  show  any time
  String? selectedCategoryName;
  List<Skill>? selectedSkills;
  int skillTabSelection = 0;

  @override
  void initState() {
    selectedPage = 0;
    _pageController = PageController(initialPage: selectedPage);
    FirebaseAnalytics.instance.setCurrentScreen(screenName: 'Dashboard_Screen');
    FirebaseAnalytics.instance.logEvent(
        name: 'user_id-${Preference.getInt(Preference.USER_ID)}',
        parameters: {
          "page_name": 'dashboard',
        });
    apiMain();
    Preference.setString(Preference.AGE_GROUP, 'teen');

    super.initState();
    if (APK_DETAILS["help_emable"] == "1") {
      if (Preference.getInt(Preference.HELP_ENABLE) != 1) executeAfterDelay();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void apiMain() async {
    await Future.wait([
      // getDomainList(),
      //getMyJobList(), //hide now for job issue... all job data come in matching_jobs kiy in g-dashboard api
      getCompetitionList(),
      getDashboardIsVisible(),
      getDasboardList()
    ]);
  }

  void executeAfterDelay() async {
    await Future.delayed(Duration(seconds: 3)); // Change the duration as needed
    // This code will execute after 2 seconds
    AlertsWidget.helpMecFutureCustomDialog(
        context: context,
        title: tr('help_message'),
        oKText: tr('go'),
        onCancelClick: () {
          Preference.setInt(Preference.HELP_ENABLE, 1);
        },
        onOkClick: () async {
          Preference.setInt(Preference.HELP_ENABLE, 1);
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => TermsAndCondition(
                        url: '${APK_DETAILS['help_mec_future']}' +
                            Preference.getInt(Preference.USER_ID).toString(),
                        title: tr('help_mec_future'),
                      ),
                  maintainState: false));
        });
  }

  Future<void> checkFeatureDiscoveryStatus() async {
    bool isFeatureDiscoveryShown =
        Preference.getBool(Preference.isProfileShown, def: true) ?? true;

    if (isFeatureDiscoveryShown) {
      FeatureDiscovery.discoverFeatures(
        context,
        const <String>{
          profileTour,
        },
      );
      Preference.setBool(Preference.isProfileShown, false);
    }
  }

  Future<void> getMyJobList() async {
    BlocProvider.of<HomeBloc>(context).add(
        JobCompListEvent(isPopular: true, isFilter: false, isJob: 1, myJob: 0));
  }

  Future<void> getCompetitionList() async {
    BlocProvider.of<HomeBloc>(context)
        .add(CompetitionListEvent(isPopular: false));
  }

  Future<void> getDomainList() async {
    BlocProvider.of<HomeBloc>(context).add(DomainListEvent());
  }

  void jobApply(int jobId, int? isApplied) {
    BlocProvider.of<HomeBloc>(context).add(CompetitionContentListEvent(
        competitionId: jobId, isApplied: isApplied));
  }

  void _handlecompetitionListResponse(CompetitionListState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          break;
        case ApiStatus.SUCCESS:
          competitionResponse = state.competitonResponse;
          completedCompetition = state.competedCompetition;
          myActivity = state.myActivity;
          break;
        case ApiStatus.ERROR:
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleOpenUrlState(ZoomOpenUrlState state) {
    switch (state.apiState) {
      case ApiStatus.LOADING:
        isJoinClassLoading = true;
        setState(() {});
        break;
      case ApiStatus.SUCCESS:
        isJoinClassLoading = false;
        setState(() {});
        if (currentZoomUrl != null) return;

        if (state.response?.status == 0) {
          if (currentOpenUrl != null)
            launchUrl(Uri.parse('$currentOpenUrl'),
                mode: LaunchMode.externalApplication);
          else if (currentZoomUrl != null)
            launchUrl(Uri.parse('$currentZoomUrl'),
                mode: LaunchMode.externalApplication);
        } else if (state.response?.data?.list?.joinUrl != null)
          launchUrl(Uri.parse('${state.response?.data?.list?.joinUrl}'),
              mode: LaunchMode.externalApplication);
        else if (currentOpenUrl != null)
          launchUrl(Uri.parse('$currentOpenUrl'),
              mode: LaunchMode.externalApplication);
        else
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('class_not_started_yet').tr(),
          ));
        break;

      case ApiStatus.ERROR:
        isJoinClassLoading = false;
        setState(() {});
        break;
      case ApiStatus.INITIAL:
        break;
    }
  }

  void _handleFeaturedInternshipsListResponse(JobCompListState state) {
    var jobCompState = state;
    setState(() {
      switch (jobCompState.apiState) {
        case ApiStatus.LOADING:
          featuredInternshipsLoading = true;
          break;
        case ApiStatus.SUCCESS:
          featuredInternshipsResponse = state.myJobListResponse;
          featuredInternshipsLoading = false;
          break;
        case ApiStatus.ERROR:
          featuredInternshipsLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleJobApplyState(CompetitionContentListState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          jobApplyLoading = true;
          break;
        case ApiStatus.SUCCESS:
          FirebaseAnalytics.instance
              .logEvent(name: 'jobs_internships', parameters: {
            "job_apply_dashboard": 'Apply',
          });
          jobApplyLoading = false;

          break;
        case ApiStatus.ERROR:
          jobApplyLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    var pages = {
      //"learner_dashboard_jobportfolio": renderMatchingJobProfile(),
      "learner_dashboard_banner": renderTopBanner(),
      "learner_dashboard_futuretrends": renderFutureTrends(),
      "learner_dashboard_interestarea": renderInterestArea(),
      "learner_dashboard_jobdomains": renderIndustryDomain(),
      "learner_dashboard_internship": renderJobInternship(),
      "learner_dashboard_competetion": renderCompetitions(),
      "learner_dashboard_portfolio": renderProtfolio(),
      "dashboard_featured_content_limit": renderFeaturedContentLimit(),
      "to_do_activities": renderRecenetActivites(),
      "dashboard_sessions_limit": renderSession(),
      "dashboard_my_courses_limit": renderMyCourses(),
      "dashboard_reels_limit": renderReels(),
      "dashboard_recommended_courses_limit": renderRecommandedCourses(),
      "dashboard_carvan_limit": renderCarvaanPageView(),
    };

    return Scaffold(
        key: _scaffoldKey,
        backgroundColor: ColorConstants.DASHBOARD_BG_COLOR,
        endDrawer: new AppDrawer(onActionFinish: () {
          getDasboardList();
          getMyJobList();
          getCompetitionList();
        }),
        body: Consumer2<VideoPlayerProvider, MenuListProvider>(
            builder: (context, value, mp, child) => MultiBlocListener(
                  listeners: [
                    BlocListener<HomeBloc, HomeState>(
                      listener: (context, state) async {
                        if (state is CompetitionListState) {
                          _handlecompetitionListResponse(state);
                        }
                        if (state is JobCompListState) {
                          _handleFeaturedInternshipsListResponse(state);
                        }
                        if (state is ZoomOpenUrlState)
                          handleOpenUrlState(state);

                        setState(() {
                          menuProvider = mp;
                        });
                      },
                    ),
                    BlocListener<DashboardContentCubit, DashboardContentState>(
                      listener: (context, state) {
                        handleDasboardList(state);
                      },
                    ),
                  ],
                  child: ScreenWithLoader(
                    isLoading: isJoinClassLoading,
                    body: SingleChildScrollView(
                      child: Stack(
                        children: [
                          Transform.translate(
                            offset: Offset(
                                0,
                                APK_DETAILS["set_goal_dashboard"] == "1"
                                    ? 0
                                    : -14),
                            //offset: Offset(0, -14),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                //jobDashboardWidgets(), //TODO: Oman Job View
                                //SizedBox(height: 40,),
                                //renderUserProfileDtl(),
                                //TODO: Render Dynamic widgets
                                APK_DETAILS["set_goal_dashboard"] == "1"
                                    ? SetGoalsAssessmentCard(
                                        onGoalUpdated: () {
                                          setState(() {});
                                        },
                                      )
                                    : SizedBox(),

                                if (APK_DETAILS[
                                        "set_goal_skill_data_dashboard"] ==
                                    "1") ...[
                                  if (dashboardContentResponse
                                          ?.data?.userSkillAssessment?.length !=
                                      null) ...[
                                    SizedBox(
                                      height: 5,
                                    ),
                                    renderSkillData(),
                                    SizedBox(
                                      height: 10,
                                    ),
                                  ],
                                ],

                                APK_DETAILS["set_goal_dashboard"] == "0"
                                    ? SizedBox(
                                        height: 100,
                                      )
                                    : SizedBox(
                                        height: 0,
                                      ),

                                renderWidgets(pages),

                                // Preference.getString(Preference.ROLE) != 'Learner' ? SizedBox(
                                //   height: 35,
                                // ):SizedBox(),
                                // Preference.getString(Preference.ROLE) == 'Learner' ? renderRecenetActivites() : SizedBox(),
                              ],
                            ),
                          ),
                          RoundedAppBar(
                              appBarHeight: height(context) *
                                  (Utility().isRTL(context) ? 0.18 : 0.16),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 12),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      height: 10,
                                    ),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        /*SizedBox(
                                      height: 5.0,
                                    ),*/
                                        Row(
                                          children: [
                                            // DescribedFeatureOverlay(
                                            //     featureId: profileTour,
                                            //     tapTarget: InkWell(
                                            //         onTap: () {
                                            //           FeatureDiscovery
                                            //               .dismissAll(context);
                                            //         },
                                            //         child:
                                            //             Text('view_all').tr()),
                                            //     backgroundColor: ColorConstants
                                            //         .PRIMARY_COLOR,
                                            //     backgroundOpacity: 0.7,
                                            //     title: Column(
                                            //       children: <Widget>[
                                            //         const Text(
                                            //             'This is overly long on purpose to test OverflowMode.clip!'),
                                            //         Center(
                                            //           child: Padding(
                                            //             padding:
                                            //                 const EdgeInsets
                                            //                     .all(20.0),
                                            //             child: InkWell(
                                            //               onTap: () {
                                            //                 FeatureDiscovery
                                            //                     .dismissAll(
                                            //                         context);
                                            //               },
                                            //               child: Text(
                                            //                 'Finish',
                                            //                 style: TextStyle(
                                            //                     color: Colors
                                            //                         .white),
                                            //               ),
                                            //             ),
                                            //           ),
                                            //         )
                                            //       ],
                                            //     ),
                                            //     overflowMode:
                                            //         OverflowMode.wrapBackground,
                                            //     enablePulsingAnimation: true,
                                            //     child: Padding(
                                            //       padding: const EdgeInsets
                                            //               .symmetric(
                                            //           horizontal: 10.0),
                                            //       child: Column(
                                            //         children: [
                                            //           InkWell(
                                            //             onTap: () async {
                                            //               if (Preference.getString(
                                            //                       Preference
                                            //                           .ROLE) ==
                                            //                   'Learner') {
                                            //                 Navigator.push(
                                            //                         context,
                                            //                         NextPageRoute(
                                            //                             NewPortfolioPage()))
                                            //                     .then((value) {
                                            //                   if (value != null)
                                            //                     menuProvider
                                            //                         ?.updateCurrentIndex(
                                            //                             value);
                                            //                 });
                                            //               } else {
                                            //                 Navigator.push(
                                            //                     context,
                                            //                     MaterialPageRoute(
                                            //                         builder:
                                            //                             (context) =>
                                            //                                 TermsAndCondition(
                                            //                                   url: 'https://mecfuture.mec.edu.om/hris/my-profile?user_id=' + Preference.getInt(Preference.USER_ID).toString(),
                                            //                                   title: tr('my_profile'),
                                            //                                 ),
                                            //                         maintainState:
                                            //                             false));
                                            //               }
                                            //             },
                                            //         child: ClipRRect(
                                            //           borderRadius:
                                            //               BorderRadius
                                            //                   .circular(
                                            //                       200),
                                            //           child: SizedBox(
                                            //             width: 50,
                                            //             child:
                                            //                 CachedNetworkImage(
                                            //               imageUrl:
                                            //                   '${Preference.getString(Preference.PROFILE_IMAGE)}',
                                            //               placeholder:
                                            //                   (context,
                                            //                           url) =>
                                            //                       SvgPicture
                                            //                           .asset(
                                            //                 'assets/images/default_user.svg',
                                            //                 width: 50,
                                            //               ),
                                            //               errorWidget: (context,
                                            //                       url,
                                            //                       error) =>
                                            //                   SvgPicture
                                            //                       .asset(
                                            //                 'assets/images/default_user.svg',
                                            //                 width: 50,
                                            //               ),
                                            //             ),
                                            //           ),
                                            //         ),
                                            //       ),
                                            //     ],
                                            //   ),
                                            // )),

                                            InkWell(
                                              onTap: () async {
                                                if (Preference.getString(
                                                            Preference.ROLE) ==
                                                        'Learner' ||
                                                    Preference.getString(
                                                            Preference.ROLE) ==
                                                        'Lead' ||
                                                    Preference.getString(
                                                            Preference.ROLE) ==
                                                        'Alumni') {
                                                  Navigator.push(
                                                          context,
                                                          NextPageRoute(
                                                              NewPortfolioPage()))
                                                      .then((value) {
                                                    if (value != null)
                                                      menuProvider
                                                          ?.updateCurrentIndex(
                                                              value);
                                                  });
                                                } else {
                                                  /*if (APK_DETAILS[
                                                            "package_name"] ==
                                                        "com.singulariswow.mec") {
                                                      Navigator.push(
                                                          context,
                                                          MaterialPageRoute(
                                                              builder: (context) =>
                                                                  TermsAndCondition(
                                                                    url: 'https://mecfuture.mec.edu.om/hris/my-profile?user_id=' +
                                                                        Preference.getInt(
                                                                                Preference.USER_ID)
                                                                            .toString(),
                                                                    title: tr(
                                                                        'my_profile'),
                                                                  ),
                                                              maintainState:
                                                                  false));
                                                    } else {
                                                      Navigator.push(
                                                              context,
                                                              NextPageRoute(
                                                                  NewPortfolioPage()))
                                                          .then((value) {
                                                        if (value != null)
                                                          menuProvider
                                                              ?.updateCurrentIndex(
                                                                  value);
                                                      });
                                                    }*/

                                                  Navigator.push(
                                                          context,
                                                          NextPageRoute(
                                                              NewPortfolioPage(
                                                                  expJobResume:
                                                                      false)))
                                                      .then((value) {
                                                    if (value != null)
                                                      menuProvider
                                                          ?.updateCurrentIndex(
                                                              value);
                                                  });
                                                }
                                              },
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(200),
                                                child: SizedBox(
                                                  width: 50,
                                                  height: 50,
                                                  child: CachedNetworkImage(
                                                    imageUrl:
                                                        '${Preference.getString(Preference.PROFILE_IMAGE)}',
                                                    fit: BoxFit.cover,
                                                    placeholder:
                                                        (context, url) =>
                                                            SvgPicture.asset(
                                                      'assets/images/default_user.svg',
                                                      width: 50,
                                                      height: 50,
                                                    ),
                                                    errorWidget:
                                                        (context, url, error) =>
                                                            SvgPicture.asset(
                                                      'assets/images/default_user.svg',
                                                      width: 50,
                                                      height: 50,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(width: 10),
                                            Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  width: width(context) * 0.5,
                                                  child: Text('welcome_text',
                                                          style: Styles.regular(
                                                              color:
                                                                  ColorConstants
                                                                      .WHITE,
                                                              size: 12))
                                                      .tr(),
                                                ),
                                                SizedBox(
                                                  width: width(context) * 0.5,
                                                  child: Text(
                                                    Utility().decrypted128(
                                                        '${Preference.getString(Preference.FIRST_NAME)}'),
                                                    //Utility().decrypted128('${Preference.getString(Preference.FIRST_NAME)?[0].toUpperCase()} ${Preference.getString(Preference.FIRST_NAME)?.substring(1).toLowerCase()}'),
                                                    maxLines: 1,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: Styles.bold(
                                                        color: ColorConstants
                                                            .WHITE,
                                                        size: 22),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        // DescribedFeatureOverlay(
                                        //     featureId: menuTour,
                                        //     tapTarget: InkWell(
                                        //         onTap: () {
                                        //           FeatureDiscovery.dismissAll(
                                        //               context);
                                        //           // Navigator.push(context,
                                        //           //     NextPageRoute(MyCourses()));
                                        //         },
                                        //         child: Center(
                                        //           child: SvgPicture.asset(
                                        //               'assets/images/hamburger_menu.svg',
                                        //               color: ColorConstants
                                        //                   .PRIMARY_COLOR),
                                        //         )),
                                        //     backgroundColor:
                                        //         ColorConstants.PRIMARY_COLOR,
                                        //     backgroundOpacity: 0.7,
                                        //     title: Column(
                                        //       children: <Widget>[
                                        //         const Text(
                                        //             'This is overly long on purpose to test OverflowMode.clip!'),
                                        //         InkWell(
                                        //           onTap: () {
                                        //             FeatureDiscovery.dismissAll(
                                        //                 context);
                                        //             FeatureDiscovery
                                        //                 .discoverFeatures(
                                        //               context,
                                        //               const <String>{
                                        //                 profileTour,
                                        //               },
                                        //             );
                                        //           },
                                        //           child: Padding(
                                        //             padding:
                                        //                 const EdgeInsets.all(
                                        //                     8.0),
                                        //             child: Align(
                                        //               alignment:
                                        //                   Alignment.bottomRight,
                                        //               child: Text(
                                        //                 'Next',
                                        //                 style: TextStyle(
                                        //                     color:
                                        //                         Colors.white),
                                        //               ),
                                        //             ),
                                        //           ),
                                        //         )
                                        //       ],
                                        //     ),
                                        //     overflowMode:
                                        //         OverflowMode.clipContent,
                                        //     enablePulsingAnimation: true,
                                        //     child: Expanded(
                                        //       flex: 2,
                                        //       child: Align(
                                        //         alignment:
                                        //             Utility().isRTL(context)
                                        //                 ? Alignment.topLeft
                                        //                 : Alignment.topRight,
                                        //         child: InkWell(
                                        //           onTap: () {
                                        //             _scaffoldKey.currentState
                                        //                 ?.openEndDrawer();
                                        //           },
                                        //           child: SvgPicture.asset(
                                        //               'assets/images/hamburger_menu.svg'),
                                        //         ),
                                        //       ),
                                        //     )),

                                        APK_DETAILS["notification_enable"] ==
                                                "1"
                                            ? Expanded(
                                                flex: 2,
                                                child: Align(
                                                  alignment:
                                                      Utility().isRTL(context)
                                                          ? Alignment.topLeft
                                                          : Alignment.topRight,
                                                  child: InkWell(
                                                    onTap: () async {
                                                      Navigator.push(
                                                          context,
                                                          MaterialPageRoute(
                                                              builder: (context) =>
                                                                  NotificationsScreen()));
                                                    },
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 10.0),
                                                      child: Icon(
                                                          Icons
                                                              .notifications_none_outlined,
                                                          color: Colors.white,
                                                          size: 26),
                                                    ),
                                                  ),
                                                ),
                                              )
                                            : Expanded(child: SizedBox()),
                                        SizedBox(width: 8),

                                        APK_DETAILS["qr_code_enable"] == "1"
                                            ? Expanded(
                                                flex: 2,
                                                child: Align(
                                                  alignment:
                                                      Utility().isRTL(context)
                                                          ? Alignment.topLeft
                                                          : Alignment.topRight,
                                                  child: InkWell(
                                                    onTap: () async {
                                                      var res =
                                                          await SimpleBarcodeScanner
                                                              .scanBarcode(
                                                                  context);
                                                      if (res != null) {
                                                        if (res
                                                            .toString()
                                                            .contains(
                                                                'mec.edu.om')) {
                                                          AppLinkRoute
                                                              .handleRoute(
                                                                  route: res);
                                                        } else {
                                                          Utility.showSnackBar(
                                                              scaffoldContext:
                                                                  context,
                                                              message: tr(
                                                                  'scanning_msg'));
                                                        }
                                                      }
                                                    },
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 10.0),
                                                      child: Icon(
                                                          Icons.qr_code_scanner,
                                                          color: Colors.white,
                                                          size: 26),
                                                    ),
                                                  ),
                                                ),
                                              )
                                            : Expanded(child: SizedBox()),
                                        SizedBox(width: 10),

                                        SizedBox(
                                          child: Align(
                                            alignment: Utility().isRTL(context)
                                                ? Alignment.topLeft
                                                : Alignment.topRight,
                                            child: InkWell(
                                              onTap: () {
                                                _scaffoldKey.currentState
                                                    ?.openEndDrawer();
                                              },
                                              child: Padding(
                                                padding:
                                                    Utility().isRTL(context)
                                                        ? EdgeInsets.only(
                                                            left: 6, top: 8)
                                                        : EdgeInsets.only(
                                                            right: 6,
                                                            bottom: 8,
                                                            top: 8),
                                                child: SizedBox(
                                                  // flex: 2,
                                                  child: Align(
                                                    alignment: Utility()
                                                            .isRTL(context)
                                                        ? Alignment.topLeft
                                                        : Alignment.topRight,
                                                    child: InkWell(
                                                      onTap: () {
                                                        _scaffoldKey
                                                            .currentState
                                                            ?.openEndDrawer();
                                                      },
                                                      child: SvgPicture.asset(
                                                          'assets/images/hamburger_menu.svg'),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 12),

                                    //TODO:hide for All app 18-oct-2023 -singh
                                    /*if(Preference.getString(Preference.ROLE) == 'Learner')
                                      Container(
                                        height: 5,
                                        width: MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            color: ColorConstants.WHITE
                                                .withValues(alpha: 0.2),
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                        child: Stack(
                                          children: [
                                            Container(
                                              height: 10,
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  ((Preference.getInt(Preference
                                                              .PROFILE_PERCENT) ??
                                                          0) /
                                                      100),
                                              decoration: BoxDecoration(
                                                  color: Color(0xffFFB72F),
                                                  borderRadius:
                                                      BorderRadius.circular(10)),
                                            ),
                                          ],
                                        ),
                                      ),
                                      if(Preference.getString(Preference.ROLE) == 'Learner')
                                      SizedBox(height: 7),
                                      if(Preference.getString(Preference.ROLE) == 'Learner')
                                      Text.rich(
                                          TextSpan(
                                            children: [
                                              TextSpan(
                                                  text:
                                                      '${tr('profile_complete')}: ',
                                                  style: Styles.regular(
                                                      color:
                                                          ColorConstants.WHITE)),
                                              TextSpan(
                                                text:
                                                    '${Preference.getInt(Preference.PROFILE_PERCENT) ?? 0}%',
                                                style: Styles.bold(
                                                    color: ColorConstants.WHITE),
                                              ),
                                            ],
                                          ),
                                          textAlign: TextAlign.left),*/
                                  ],
                                ),
                              )),
                        ],
                      ),
                    ),
                  ),
                )));
  }

  futureTrendsList() {
    List<FutureTrends>? domainList =
        dashboardContentResponse?.data?.futureTrends;
    if (domainList == null || domainList.length == 0) return SizedBox();
    return Container(
      decoration: BoxDecoration(color: ColorConstants.WHITE),
      child: Column(
        children: [
          /*SizedBox(
            height: height(context) * 0.060,
          ),*/
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                  padding: Utility().isRTL(context)
                      ? EdgeInsets.only(right: 15.0)
                      : EdgeInsets.only(left: 15.0),
                  child: Icon(Icons.trending_up_outlined, size: 18)
                  //  SvgPicture.asset(
                  //   'assets/images/grf_job.svg',
                  //   height: 18,
                  //   width: 18,
                  //   allowDrawingOutsideViewBox: true,
                  // ),
                  ),
              Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 10,
                  ),
                  child: Text(
                    'future_trend',
                    style: Styles.bold(
                        size: 14, color: ColorConstants.HEADING_PRIMARY_COLOR),
                  ).tr()),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Container(
              height: 85,
              child: ListView.builder(
                  itemCount: domainList.length,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (BuildContext context, int index) {
                    return InkWell(
                      onTap: () {
                        FirebaseAnalytics.instance
                            .logEvent(name: 'future_trend', parameters: {
                          "trend": domainList[index].name ?? '',
                          "organization_id":
                              domainList[index].organizationId ?? ''
                        });
                        futureTrendsButtonSheet(
                            domainList[index].name ?? '',
                            domainList[index].jobCount.toString(),
                            domainList[index].growthType ?? '',
                            domainList[index].growth ?? '0',
                            domainList[index].id);
                      },
                      child: Container(
                        width: min(width(context), 480) * 0.4,
                        decoration: BoxDecoration(
                          color: ColorConstants()
                              .gradientRight()
                              .withValues(alpha: 0.08),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        margin: EdgeInsets.all(8),
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 8.0, right: 8.0, top: 8.0, bottom: 8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: width(context) * 0.4,
                                child: Center(
                                  child: Text(
                                    '${domainList[index].name}',
                                    style: Styles.bold(
                                        color: Color(0xff0E1638), size: 13),
                                    softWrap: true,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 5,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    '${domainList[index].jobCount} ${tr('job_roles')} ',
                                    style: Styles.regular(
                                        color: ColorConstants.GREY_3, size: 11),
                                  ),
                                  Padding(
                                    padding: EdgeInsets.only(
                                        left: !Utility().isRTL(context)
                                            ? 0
                                            : 8.0),
                                    child: Text(
                                      domainList[index].growthType == 'up'
                                          ? ' + ${domainList[index].growth ?? ' 0'}%'
                                          : domainList[index].growth != null
                                              ? ' - ${domainList[index].growth ?? ' 0'}%'
                                              : '${domainList[index].growth ?? ' 0'}%',
                                      style: Styles.regular(
                                          color: domainList[index].growthType ==
                                                  'up'
                                              ? ColorConstants.GREEN
                                              : ColorConstants.RED,
                                          size: 11),
                                    ),
                                  ),
                                  Transform.translate(
                                      offset: Offset(
                                          Utility().isRTL(context) ? 10.0 : 0,
                                          0),
                                      child:
                                          domainList[index].growthType == 'up'
                                              ? Icon(
                                                  Icons.arrow_drop_up_outlined,
                                                  color: Colors.green,
                                                  size: 20,
                                                )
                                              : Icon(
                                                  Icons.arrow_drop_down,
                                                  color: Colors.red,
                                                  size: 20,
                                                )),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }),
            ),
          ),
          SizedBox(
            height: 10,
          ),
        ],
      ),
    );
  }

  futureTrendsButtonSheet(String title, String jobsCount, String growthType,
      String growth, int domainId) {
    return showModalBottomSheet(
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30), topRight: Radius.circular(30))),
        backgroundColor: Colors.white,
        context: context,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return Stack(
            children: [
              Positioned(
                right: 10,
                child: IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: Icon(Icons.close)),
              ),
              Container(
                height: MediaQuery.of(context).size.height * 0.7,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width * 0.6,
                      decoration: BoxDecoration(
                          color: ColorConstants()
                              .gradientRight()
                              .withValues(alpha: 0.08),
                          border: Border.all(color: ColorConstants.List_Color)),
                      margin: EdgeInsets.all(8),
                      // color: Colors.red,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                        child: Column(
                          children: [
                            Center(
                              child: Text(
                                '$title',
                                style: Styles.bold(
                                    color: Color(0xff0E1638), size: 13),
                                softWrap: true,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            SizedBox(
                              height: 5,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '$jobsCount ${tr('job_roles')} ',
                                  style: Styles.regular(
                                      color: ColorConstants.GREY_3, size: 11),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(
                                      left:
                                          !Utility().isRTL(context) ? 0 : 8.0),
                                  child: Text(
                                    growthType == 'up'
                                        ? ' + $growth%'
                                        : growth == '0'
                                            ? ' $growth%'
                                            : ' - $growth%',
                                    style: Styles.regular(
                                        color: growthType == 'up'
                                            ? ColorConstants.GREEN
                                            : ColorConstants.RED,
                                        size: 11),
                                  ),
                                ),
                                Transform.translate(
                                    offset: Offset(
                                        Utility().isRTL(context) ? 10.0 : 0, 0),
                                    child: growthType == 'up'
                                        ? Icon(
                                            Icons.arrow_drop_up_outlined,
                                            color: Colors.green,
                                            size: 20,
                                          )
                                        : Icon(
                                            Icons.arrow_drop_down,
                                            color: Colors.red,
                                            size: 20,
                                          )),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                        child: LineChartWidget(
                      domainid: domainId,
                    )),
                  ],
                ),
              ),
            ],
          );
        });
  }

  invitedJobsCard() {
    /* int? visibleCount = matchingJobsResp?.where((e) {
      return e.jobStatus == null || e.jobStatus == "";
    }).length;*/
    //if (visibleCount == 0) return SizedBox();

    return Container(
      child: Column(
        children: [
          Container(
            color: ColorConstants.WHITE,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(13.0),
                  child: Row(
                    children: [
                      Icon(
                        Icons.work_outline_outlined,
                        size: 18,
                        color: ColorConstants.HEADING_PRIMARY_COLOR,
                      ),
                      SizedBox(width: 8),
                      Text('jobs_internships',
                              style: Styles.bold(
                                  size: 14,
                                  color: ColorConstants.HEADING_PRIMARY_COLOR))
                          .tr(),
                      Spacer(),
                      InkWell(
                        onTap: () {
                          Navigator.push(
                                  context,
                                  NextPageRoute(
                                      JobDashboardPage(
                                        myJobEnable: false,
                                      ),
                                      isMaintainState: true))
                              .then((value) {
                            setState(() {
                              getMyJobList();
                            });
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: Text('view_all',
                              style: Styles.regular(
                                size: 12,
                                color: ColorConstants.BODY_TEXT,
                              )).tr(),
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(
                  height: 1,
                  color: ColorConstants.GREY_10,
                  thickness: 1,
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0),
            child: matchingJobsResp != null && matchingJobsResp?.length != 0
                ? Container(
                    color: ColorConstants.WHITE,
                    // height: height(context) > 700
                    //     ? MediaQuery.of(context).size.height * 0.35
                    //     : height(context) * 0.44,
                    child: ListView.builder(
                        physics: NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount:
                            min(4, int.parse('${matchingJobsResp?.length}')),
                        scrollDirection: Axis.vertical,
                        itemBuilder: (BuildContext context, int index) {
                          return InkWell(
                            onTap: () {
                              Navigator.push(
                                  context,
                                  NextPageRoute(JobDetailsPage(
                                    title: matchingJobsResp![index].name,
                                    description:
                                        matchingJobsResp![index].description,
                                    location:
                                        matchingJobsResp![index].workAddress,
                                    skillNames:
                                        matchingJobsResp![index].skillNames,
                                    companyName:
                                        matchingJobsResp![index].organizedBy,
                                    domain: matchingJobsResp![index].domainName,
                                    companyThumbnail:
                                        matchingJobsResp![index].image,
                                    experience:
                                        matchingJobsResp![index].experience,
                                    jobStatusNumeric: matchingJobsResp![index]
                                        .jobStatusNumeric,
                                    id: matchingJobsResp![index].id,
                                    jobStatus:
                                        matchingJobsResp![index].jobStatus,
                                    vacancy:
                                        matchingJobsResp![index].numOfVacancy,
                                    minExperience:
                                        matchingJobsResp![index].minExperience,
                                    maxExperience:
                                        matchingJobsResp![index].maxExperience,
                                  )));
                            },
                            child: Column(
                              children: [
                                Container(
                                  width: double.infinity,
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 16),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          flex: 3,
                                          child: Container(
                                            padding: EdgeInsets.only(
                                              right: 10.0,
                                            ),
                                            child: matchingJobsResp?[index]
                                                        .image !=
                                                    null
                                                ? Image.network(
                                                    '${matchingJobsResp?[index].image}',
                                                  )
                                                : Image.asset(
                                                    'assets/images/pb_2.png'),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 9,
                                          child: Container(
                                            padding: EdgeInsets.only(
                                                left: 5.0, right: 5.0),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  '${matchingJobsResp![index].name ?? ''}',
                                                  style: Styles.bold(
                                                      size: 16,
                                                      color: ColorConstants
                                                          .HEADING_TITLE),
                                                ),
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          top: 6.0, right: 5.0),
                                                  child: Text(
                                                    '${matchingJobsResp![index].organizedBy ?? ''}',
                                                    style: Styles.regular(
                                                      size: 13,
                                                      color: ColorConstants
                                                          .SUB_HEADING_TITLE,
                                                    ),
                                                  ),
                                                ),
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          top: 5.0),
                                                  child: Row(
                                                    children: [
                                                      matchingJobsResp![index]
                                                                      .minExperience !=
                                                                  null ||
                                                              matchingJobsResp![
                                                                          index]
                                                                      .maxExperience !=
                                                                  null
                                                          ? SizedBox(
                                                              child: Row(
                                                                children: [
                                                                  Icon(
                                                                    Icons
                                                                        .work_outline,
                                                                    size: 14,
                                                                    color: ColorConstants
                                                                        .BODY_TEXT,
                                                                  ),
                                                                  Padding(
                                                                    padding:
                                                                        EdgeInsets
                                                                            .only(
                                                                      left: Utility()
                                                                              .isRTL(context)
                                                                          ? 0
                                                                          : 5.0,
                                                                      right: Utility()
                                                                              .isRTL(context)
                                                                          ? 5.0
                                                                          : 0.0,
                                                                    ),
                                                                    child: Text(
                                                                      '${tr('exp')}: ',
                                                                      style: Styles
                                                                          .regular(
                                                                        size:
                                                                            13,
                                                                        color: ColorConstants
                                                                            .BODY_TEXT,
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  Text(
                                                                      '${matchingJobsResp![index].minExperience != null ? matchingJobsResp![index].minExperience : "0"}' +
                                                                          '-${matchingJobsResp![index].maxExperience != null ? matchingJobsResp![index].maxExperience : "0"} ${tr('yrs')} ',
                                                                      style: Styles.regular(
                                                                          size:
                                                                              12,
                                                                          color:
                                                                              ColorConstants.GREY_3)),

                                                                  /*Text(
                                                                  '${featuredInternshipsResponse?.data![index]?.experience != null ? featuredInternshipsResponse?.data![index]?.experience : "0"} ${tr('yrs')}',
                                                                  style: Styles
                                                                      .regular(
                                                                    size: 13,
                                                                    color: ColorConstants
                                                                        .BODY_TEXT,
                                                                  ),
                                                                ),*/
                                                                ],
                                                              ),
                                                            )
                                                          : SizedBox(),
                                                      if (matchingJobsResp![
                                                                      index]
                                                                  .workAddress !=
                                                              null &&
                                                          matchingJobsResp![
                                                                      index]
                                                                  .workAddress !=
                                                              "") ...[
                                                        Row(
                                                          children: [
                                                            Padding(
                                                              padding:
                                                                  EdgeInsets
                                                                      .only(
                                                                left: Utility()
                                                                        .isRTL(
                                                                            context)
                                                                    ? 0
                                                                    : 5.0,
                                                                right: Utility()
                                                                        .isRTL(
                                                                            context)
                                                                    ? 5.0
                                                                    : 0.0,
                                                              ),
                                                              child: Icon(
                                                                Icons
                                                                    .location_on_outlined,
                                                                size: 14,
                                                                color: ColorConstants
                                                                    .BODY_TEXT,
                                                              ),
                                                            ),
                                                            SizedBox(
                                                              child: Text(
                                                                '${matchingJobsResp![index].workAddress}',
                                                                softWrap: true,
                                                                overflow:
                                                                    TextOverflow
                                                                        .ellipsis,
                                                                style: Styles
                                                                    .regular(
                                                                  size: 13,
                                                                  color: ColorConstants
                                                                      .BODY_TEXT,
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ],
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 2,
                                          child: matchingJobsResp![
                                                              index]
                                                          .jobStatus ==
                                                      null ||
                                                  matchingJobsResp![index]
                                                          .jobStatus ==
                                                      "" ||
                                                  matchingJobsResp![index]
                                                          .jobStatus
                                                          ?.toLowerCase() ==
                                                      "pending"
                                              ? InkWell(
                                                  onTap: () {
                                                    FirebaseAnalytics.instance
                                                        .logEvent(
                                                      name: 'jobs_internships',
                                                      parameters: {
                                                        "job_apply_dashboard":
                                                            'Apply',
                                                        "job_name":
                                                            matchingJobsResp![
                                                                        index]
                                                                    .name ??
                                                                '',
                                                      },
                                                    );
                                                    //jobApply(int.parse('${matchingJobsResp![index].programId}',), 1,);
                                                    jobApply(
                                                      int.parse(
                                                        '${matchingJobsResp![index].id}',
                                                      ),
                                                      1,
                                                    );
                                                    setState(() {
                                                      matchingJobsResp!
                                                          .removeAt(index);
                                                    });
                                                    _onLoadingForJob();
                                                  },
                                                  child: Container(
                                                    // padding: EdgeInsets.only(
                                                    //     left: 8),
                                                    child: GradientText(
                                                      tr('apply_button'),
                                                      style:
                                                          Styles.bold(size: 14),
                                                      colors: [
                                                        ColorConstants()
                                                            .gradientLeft(),
                                                        ColorConstants()
                                                            .gradientRight(),
                                                      ],
                                                    ),
                                                  ),
                                                )
                                              : Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          bottom: 20.0),
                                                  child: Text(
                                                    'applied',
                                                    style: Styles.bold(
                                                        color: Colors.green,
                                                        size: 12),
                                                  ).tr(),
                                                ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                Divider(
                                  height: 1,
                                  thickness: 1,
                                  color: ColorConstants.GREY_10,
                                ),
                              ],
                            ),
                          );
                        }))
                : SizedBox(),
          ),
        ],
      ),
    );
  }

  featuredJobsInternships() {
    int? visibleCount = featuredInternshipsResponse?.data?.where((e) {
      return e?.jobStatus == null || e?.jobStatus == "";
    }).length;
    if (visibleCount == 0) return SizedBox();

    return Container(
      child: Column(
        children: [
          if (featuredInternshipsResponse?.data != null)
            Container(
              color: ColorConstants.WHITE,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(13.0),
                    child: Row(
                      children: [
                        Icon(
                          Icons.work_outline_outlined,
                          size: 18,
                          color: ColorConstants.HEADING_PRIMARY_COLOR,
                        ),
                        SizedBox(width: 8),
                        Text('jobs_internships',
                                style: Styles.bold(
                                    size: 14,
                                    color:
                                        ColorConstants.HEADING_PRIMARY_COLOR))
                            .tr(),
                        Spacer(),
                        InkWell(
                          onTap: () {
                            Navigator.push(
                                    context,
                                    NextPageRoute(
                                        JobDashboardPage(
                                          myJobEnable: false,
                                        ),
                                        isMaintainState: true))
                                .then((value) {
                              setState(() {
                                getMyJobList();
                              });
                            });
                          },
                          child: Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: Text('view_all',
                                style: Styles.regular(
                                  size: 12,
                                  color: ColorConstants.BODY_TEXT,
                                )).tr(),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: ColorConstants.GREY_10,
                    thickness: 1,
                  ),
                ],
              ),
            ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0),
            child: featuredInternshipsResponse?.data!.length != 0 &&
                    featuredInternshipsResponse?.data != null
                ? Container(
                    color: ColorConstants.WHITE,
                    // height: height(context) > 700
                    //     ? MediaQuery.of(context).size.height * 0.35
                    //     : height(context) * 0.44,
                    child: ListView.builder(
                        physics: NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: min(
                            2,
                            int.parse(
                                '${featuredInternshipsResponse?.data!.length}')),
                        scrollDirection: Axis.vertical,
                        itemBuilder: (BuildContext context, int index) {
                          return featuredInternshipsResponse
                                          ?.data![index]!.jobStatus ==
                                      null ||
                                  featuredInternshipsResponse
                                          ?.data![index]!.jobStatus ==
                                      ""
                              ? InkWell(
                                  onTap: () {
                                    FirebaseAnalytics.instance.logEvent(
                                        name: 'jobs_internships',
                                        parameters: {
                                          "job_view":
                                              featuredInternshipsResponse
                                                      ?.data![index]!.name ??
                                                  '',
                                        });

                                    Navigator.push(
                                        context,
                                        NextPageRoute(JobDetailsPage(
                                          title: featuredInternshipsResponse
                                              ?.data![index]!.name,
                                          description:
                                              featuredInternshipsResponse
                                                  ?.data![index]!.description,
                                          location: featuredInternshipsResponse
                                              ?.data![index]!.location,
                                          skillNames:
                                              featuredInternshipsResponse
                                                  ?.data![index]!.skillNames,
                                          companyName:
                                              featuredInternshipsResponse
                                                  ?.data![index]!.organizedBy,
                                          domain: featuredInternshipsResponse
                                              ?.data![index]!.domainName,
                                          companyThumbnail:
                                              featuredInternshipsResponse
                                                  ?.data![index]!.image,
                                          experience:
                                              featuredInternshipsResponse
                                                  ?.data![index]!.experience,
                                          jobStatusNumeric:
                                              featuredInternshipsResponse
                                                  ?.data![index]!
                                                  .jobStatusNumeric,
                                          id: featuredInternshipsResponse
                                              ?.data![index]!.id,
                                          jobStatus: featuredInternshipsResponse
                                              ?.data![index]!.jobStatus,
                                          vacancy: featuredInternshipsResponse
                                              ?.data![index]!.vacancy,
                                          minExperience:
                                              featuredInternshipsResponse
                                                  ?.data![index]!.minExperience,
                                          maxExperience:
                                              featuredInternshipsResponse
                                                  ?.data![index]!.maxExperience,
                                        )));
                                  },
                                  child: Column(
                                    children: [
                                      Container(
                                        width: double.infinity,
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 16, vertical: 16),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                flex: 3,
                                                child: Container(
                                                  padding: EdgeInsets.only(
                                                    right: 10.0,
                                                  ),
                                                  child: featuredInternshipsResponse
                                                              ?.data![index]
                                                              ?.image !=
                                                          null
                                                      ? Image.network(
                                                          '${featuredInternshipsResponse?.data![index]!.image}',
                                                        )
                                                      : Image.asset(
                                                          'assets/images/pb_2.png'),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 9,
                                                child: Container(
                                                  padding: EdgeInsets.only(
                                                      left: 5.0, right: 5.0),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        '${featuredInternshipsResponse?.data![index]?.name ?? ''}',
                                                        style: Styles.bold(
                                                            size: 16,
                                                            color: ColorConstants
                                                                .HEADING_TITLE),
                                                      ),
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                top: 6.0,
                                                                right: 5.0),
                                                        child: Text(
                                                          '${featuredInternshipsResponse?.data![index]?.organizedBy ?? ''}',
                                                          style: Styles.regular(
                                                            size: 13,
                                                            color: ColorConstants
                                                                .SUB_HEADING_TITLE,
                                                          ),
                                                        ),
                                                      ),
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(top: 5.0),
                                                        child: Row(
                                                          children: [
                                                            featuredInternshipsResponse
                                                                            ?.data![
                                                                                index]
                                                                            ?.minExperience !=
                                                                        null ||
                                                                    featuredInternshipsResponse
                                                                            ?.data![index]
                                                                            ?.maxExperience !=
                                                                        null
                                                                ? SizedBox(
                                                                    child: Row(
                                                                      children: [
                                                                        Icon(
                                                                          Icons
                                                                              .work_outline,
                                                                          size:
                                                                              16,
                                                                          color:
                                                                              ColorConstants.BODY_TEXT,
                                                                        ),
                                                                        Padding(
                                                                          padding:
                                                                              EdgeInsets.only(
                                                                            left: Utility().isRTL(context)
                                                                                ? 0
                                                                                : 5.0,
                                                                            right: Utility().isRTL(context)
                                                                                ? 5.0
                                                                                : 0.0,
                                                                          ),
                                                                          child:
                                                                              Text(
                                                                            '${tr('exp')}: ',
                                                                            style:
                                                                                Styles.regular(
                                                                              size: 13,
                                                                              color: ColorConstants.BODY_TEXT,
                                                                            ),
                                                                          ),
                                                                        ),
                                                                        Text(
                                                                            '${featuredInternshipsResponse?.data![index]?.minExperience != null ? featuredInternshipsResponse?.data![index]?.minExperience : "0"}' +
                                                                                '-${featuredInternshipsResponse?.data![index]?.maxExperience != null ? featuredInternshipsResponse?.data![index]?.maxExperience : "0"} ${tr('yrs')} ',
                                                                            style:
                                                                                Styles.regular(size: 12, color: ColorConstants.GREY_3)),

                                                                        /*Text(
                                                                    '${featuredInternshipsResponse?.data![index]?.experience != null ? featuredInternshipsResponse?.data![index]?.experience : "0"} ${tr('yrs')}',
                                                                    style: Styles
                                                                        .regular(
                                                                      size: 13,
                                                                      color: ColorConstants
                                                                          .BODY_TEXT,
                                                                    ),
                                                                  ),*/
                                                                      ],
                                                                    ),
                                                                  )
                                                                : SizedBox(),
                                                            if (featuredInternshipsResponse
                                                                    ?.data![
                                                                        index]
                                                                    ?.location !=
                                                                null) ...[
                                                              Row(
                                                                children: [
                                                                  Padding(
                                                                    padding:
                                                                        EdgeInsets
                                                                            .only(
                                                                      left: Utility()
                                                                              .isRTL(context)
                                                                          ? 0
                                                                          : 20.0,
                                                                      right: Utility()
                                                                              .isRTL(context)
                                                                          ? 20.0
                                                                          : 0.0,
                                                                    ),
                                                                    child: Icon(
                                                                      Icons
                                                                          .location_on_outlined,
                                                                      size: 16,
                                                                      color: ColorConstants
                                                                          .BODY_TEXT,
                                                                    ),
                                                                  ),
                                                                  Text(
                                                                    '${featuredInternshipsResponse?.data![index]?.location}',
                                                                    style: Styles
                                                                        .regular(
                                                                      size: 13,
                                                                      color: ColorConstants
                                                                          .BODY_TEXT,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ],
                                                          ],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: featuredInternshipsResponse
                                                                ?.data![index]
                                                                ?.jobStatus ==
                                                            null ||
                                                        featuredInternshipsResponse
                                                                ?.data![index]
                                                                ?.jobStatus ==
                                                            ""
                                                    ? InkWell(
                                                        onTap: () {
                                                          FirebaseAnalytics
                                                              .instance
                                                              .logEvent(
                                                            name:
                                                                'jobs_internships',
                                                            parameters: {
                                                              "job_apply_dashboard":
                                                                  'Apply',
                                                              "job_name": featuredInternshipsResponse
                                                                      ?.data![
                                                                          index]
                                                                      ?.name ??
                                                                  '',
                                                            },
                                                          );
                                                          jobApply(
                                                            int.parse(
                                                              '${featuredInternshipsResponse?.data![index]?.id}',
                                                            ),
                                                            1,
                                                          );
                                                          setState(() {
                                                            featuredInternshipsResponse
                                                                ?.data
                                                                ?.removeAt(
                                                                    index);
                                                          });
                                                          _onLoadingForJob();
                                                        },
                                                        child: Container(
                                                          padding:
                                                              EdgeInsets.only(
                                                                  left: 12),
                                                          child: GradientText(
                                                            tr('apply_button'),
                                                            style: Styles.bold(
                                                                size: 14),
                                                            colors: [
                                                              ColorConstants()
                                                                  .gradientLeft(),
                                                              ColorConstants()
                                                                  .gradientRight(),
                                                            ],
                                                          ),
                                                        ),
                                                      )
                                                    : Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                bottom: 20.0),
                                                        child: Text(
                                                          'applied',
                                                          style: Styles.bold(
                                                              color:
                                                                  Colors.green,
                                                              size: 12),
                                                        ).tr(),
                                                      ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      Divider(
                                        height: 1,
                                        thickness: 1,
                                        color: ColorConstants.GREY_10,
                                      ),
                                    ],
                                  ),

                                  // Container(
                                  //   width: min(width(context), 480) * 0.7,
                                  //   decoration: BoxDecoration(
                                  //       color: ColorConstants.WHITE,
                                  //       borderRadius: BorderRadius.circular(10),
                                  //       border: Border.all(
                                  //           color: ColorConstants.List_Color)),
                                  //   margin: EdgeInsets.all(8),
                                  //   child: Column(
                                  //       crossAxisAlignment:
                                  //           CrossAxisAlignment.start,
                                  //       mainAxisAlignment:
                                  //           MainAxisAlignment.start,
                                  //       children: [
                                  //         Padding(
                                  //           padding: EdgeInsets.only(
                                  //               left: 8.0,
                                  //               right: 8.0,
                                  //               top: 8.0,
                                  //               bottom: 8.0),
                                  //           child: Column(
                                  //             crossAxisAlignment:
                                  //                 CrossAxisAlignment.start,
                                  //             children: [
                                  //               CachedNetworkImage(
                                  //                 imageUrl:
                                  //                     '${featuredInternshipsResponse?.data![index]!.image}',
                                  //                 width: 70,
                                  //                 height: 60,
                                  //                 errorWidget:
                                  //                     (context, url, error) =>
                                  //                         SvgPicture.asset(
                                  //                   'assets/images/exp_emp.svg',
                                  //                 ),
                                  //                 fit: BoxFit.fill,
                                  //               ),
                                  //               SizedBox(
                                  //                 height: 46,
                                  //                 child: Padding(
                                  //                   padding:
                                  //                       const EdgeInsets.only(
                                  //                           top: 7.0),
                                  //                   child: Text(
                                  //                     '${featuredInternshipsResponse?.data![index]!.name}',
                                  //                     maxLines: 2,
                                  //                     style: Styles.bold(
                                  //                         lineHeight: 1.4,
                                  //                         color:
                                  //                             Color(0xff0E1638),
                                  //                         size: 14),
                                  //                   ),
                                  //                 ),
                                  //               ),
                                  //               SizedBox(
                                  //                 height: 5,
                                  //               ),
                                  //               Row(
                                  //                 mainAxisAlignment:
                                  //                     MainAxisAlignment.start,
                                  //                 children: [
                                  //                   Flexible(
                                  //                     child: Text(
                                  //                       '${featuredInternshipsResponse?.data![index]!.organizedBy}',
                                  //                       maxLines: 2,
                                  //                       softWrap: true,
                                  //                       style: Styles.regular(
                                  //                           color:
                                  //                               ColorConstants
                                  //                                   .GREY_3,
                                  //                           size: 13),
                                  //                     ),
                                  //                   ),
                                  //                 ],
                                  //               ),
                                  //               SizedBox(
                                  //                 height: 10,
                                  //               ),
                                  //               Row(
                                  //                 mainAxisAlignment:
                                  //                     MainAxisAlignment.start,
                                  //                 children: [
                                  //                   Padding(
                                  //                     padding:
                                  //                         const EdgeInsets.only(
                                  //                             left: 1.0),
                                  //                     child: Icon(
                                  //                       Icons
                                  //                           .location_on_outlined,
                                  //                       size: 16,
                                  //                       color: ColorConstants
                                  //                           .GREY_3,
                                  //                     ),
                                  //                   ),
                                  //                   Padding(
                                  //                     padding:
                                  //                         const EdgeInsets.only(
                                  //                             left: 8.0),
                                  //                     child: Text(
                                  //                       '${featuredInternshipsResponse?.data![index]!.location}',
                                  //                       style: Styles.regular(
                                  //                           color:
                                  //                               ColorConstants
                                  //                                   .GREY_3,
                                  //                           size: 11),
                                  //                     ),
                                  //                   ),
                                  //                 ],
                                  //               ),
                                  //               SizedBox(
                                  //                 height: 7,
                                  //               ),
                                  //               Row(
                                  //                 mainAxisAlignment:
                                  //                     MainAxisAlignment.start,
                                  //                 children: [
                                  //                   // Image.asset(
                                  //                   //   'assets/images/jobicon.png',
                                  //                   //   height: 18,
                                  //                   //   width: 18,
                                  //                   // ),
                                  //                   Icon(Icons.work_outline,
                                  //                       size: 16,
                                  //                       color: ColorConstants
                                  //                           .GREY_6),
                                  //                   Padding(
                                  //                     padding:
                                  //                         const EdgeInsets.only(
                                  //                             left: 5.0),
                                  //                     child: Text(
                                  //                         '${tr('exp')}: ',
                                  //                         style: Styles.regular(
                                  //                             size: 12,
                                  //                             color:
                                  //                                 ColorConstants
                                  //                                     .GREY_6)),
                                  //                   ),
                                  //                   Text(
                                  //                       '${featuredInternshipsResponse?.data![index]!.experience} ${tr('yrs')}',
                                  //                       style: Styles.regular(
                                  //                           size: 12,
                                  //                           color:
                                  //                               ColorConstants
                                  //                                   .GREY_6)),
                                  //                 ],
                                  //               ),
                                  //               SizedBox(
                                  //                 height: 20,
                                  //               ),
                                  //               featuredInternshipsResponse
                                  //                               ?.data![index]!
                                  //                               .jobStatus ==
                                  //                           null ||
                                  //                       featuredInternshipsResponse
                                  //                               ?.data![index]!
                                  //                               .jobStatus ==
                                  //                           ""
                                  //                   ? InkWell(
                                  //                       onTap: () {
                                  //                         FirebaseAnalytics
                                  //                             .instance
                                  //                             .logEvent(
                                  //                                 name:
                                  //                                     'jobs_internships',
                                  //                                 parameters: {
                                  //                               "job_apply_dashboard":
                                  //                                   'Apply',
                                  //                               "job_name":
                                  //                                   featuredInternshipsResponse
                                  //                                       ?.data![
                                  //                                           index]!
                                  //                                       .name,
                                  //                             });
                                  //                         jobApply(
                                  //                             int.parse(
                                  //                                 '${featuredInternshipsResponse?.data![index]!.id}'),
                                  //                             1);
                                  //                         setState(() {
                                  //                           featuredInternshipsResponse
                                  //                               ?.data
                                  //                               ?.removeAt(
                                  //                                   index);
                                  //                         });
                                  //                         _onLoadingForJob();
                                  //                       },
                                  //                       child: Container(
                                  //                         height: 45,
                                  //                         width: MediaQuery.of(
                                  //                                 context)
                                  //                             .size
                                  //                             .width,
                                  //                         decoration:
                                  //                             BoxDecoration(
                                  //                           borderRadius:
                                  //                               BorderRadius
                                  //                                   .circular(
                                  //                                       50),
                                  //                           gradient:
                                  //                               LinearGradient(
                                  //                                   colors: [
                                  //                                 ColorConstants
                                  //                                     .DASHBOARD_APPLY_COLOR,
                                  //                                 ColorConstants
                                  //                                     .DASHBOARD_APPLY_COLOR,
                                  //                               ]),
                                  //                         ),
                                  //                         child: Row(
                                  //                           mainAxisAlignment:
                                  //                               MainAxisAlignment
                                  //                                   .center,
                                  //                           children: [
                                  //                             Text(
                                  //                               'apply_buttn',
                                  //                               style: TextStyle(
                                  //                                   color: Colors
                                  //                                       .white,
                                  //                                   fontSize:
                                  //                                       16,
                                  //                                   fontWeight:
                                  //                                       FontWeight
                                  //                                           .bold),
                                  //                             ).tr(),
                                  //                           ],
                                  //                         ),
                                  //                       ),
                                  //                     )
                                  //                   : Center(
                                  //                       child: Padding(
                                  //                         padding:
                                  //                             const EdgeInsets
                                  //                                     .only(
                                  //                                 bottom: 20.0),
                                  //                         child: Text(
                                  //                           'applied',
                                  //                           style: Styles.bold(
                                  //                               color: Colors
                                  //                                   .green,
                                  //                               size: 14),
                                  //                         ).tr(),
                                  //                       ),
                                  //                     ),
                                  //             ],
                                  //           ),
                                  //         ),
                                  //       ]),
                                  // ),
                                )
                              : SizedBox();
                        }))
                : SizedBox(),
          ),
        ],
      ),
    );
  }

  void _onLoadingForJob() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        Future.delayed(Duration(seconds: 2), () {
          Navigator.of(context).pop(true);
          Get.rawSnackbar(
            messageText: Text(
              'application_submitted',
              style: Styles.regular(size: 14, color: ColorConstants.WHITE),
            ).tr(),
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: ColorConstants.BLACK,
            borderRadius: 4,
            duration: Duration(seconds: 3),
            boxShadows: [
              BoxShadow(
                  color: Color(0xff898989).withValues(alpha: 0.1),
                  offset: Offset(0, 4.0),
                  blurRadius: 11)
            ],
          );
        });

        return Dialog(
          child: Container(
            padding: EdgeInsets.only(left: 20, right: 10),
            height: 100,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: new Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                new CircularProgressIndicator(
                  color: Colors.blue,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10.0),
                  child: new Text("${tr('job_apply')}..."),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  competitionsWidgets() {
    return Column(
      children: [
        //TODO: My Event
        Preference.getString(Preference.ROLE) == 'Presenter'
            ? Container(
                height: 100,
                margin: const EdgeInsets.only(top: 15, left: 8, right: 8),
                decoration: BoxDecoration(
                  color: ColorConstants.WHITE,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Padding(
                  padding: const EdgeInsets.only(top: 15, left: 10, right: 0),
                  child: InkWell(
                    onTap: () {
                      Navigator.push(
                          context,
                          PageTransition(
                              duration: Duration(milliseconds: 300),
                              reverseDuration: Duration(milliseconds: 300),
                              type: PageTransitionType.bottomToTop,
                              child: CompetitionMyActivity(
                                completedCompetition: completedCompetition,
                                myActivity: myActivity,
                                enableStatus: true,
                              ))).then((value) {
                        //topScoringUser();
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.calendar_month,
                                    color: Colors.black, size: 20),
                                SizedBox(width: 10),
                                Text('my_events',
                                        style: Styles.bold(
                                            size: 14,
                                            color: ColorConstants
                                                .HEADING_PRIMARY_COLOR))
                                    .tr(),
                              ],
                            ),
                            Padding(
                                padding:
                                    const EdgeInsets.only(top: 10, right: 0.0),
                                child: SizedBox(
                                  width: 250,
                                  child: Text(
                                    'my_event_note',
                                    maxLines: 3,
                                    overflow: TextOverflow.ellipsis,
                                    style: Styles.regular(size: 10),
                                  ).tr(),
                                )),
                          ],
                        ),
                        Spacer(),
                        Padding(
                            padding: const EdgeInsets.only(right: 0.0),
                            child: Image.asset(
                              "assets/images/event_vector.png",
                              opacity: AlwaysStoppedAnimation(0.5),
                              height: 100,
                              width: 100,
                            )),
                      ],
                    ),
                  ),
                ),
              )
            : SizedBox(),

        //TODO: Event
        competitionResponse?.event != null &&
                competitionResponse?.event?.length != 0
            ? Container(
                margin: EdgeInsets.only(top: isEnableEvent == true ? 8 : 0),
                decoration: BoxDecoration(color: ColorConstants.WHITE),
                padding: EdgeInsets.only(top: isEnableEvent == true ? 16 : 0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (competitionResponse?.event != null &&
                        competitionResponse?.event?.length != 0 &&
                        isEnableEvent == true)
                      Container(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  SvgPicture.asset('assets/images/event.svg'),
                                  SizedBox(width: 10),
                                  Text(
                                    'events',
                                    style: Styles.bold(
                                        color: ColorConstants.HEADING_TITLE,
                                        size: 14),
                                  ).tr(),
                                  Spacer(),
                                  InkWell(
                                    onTap: () {
                                      FirebaseAnalytics.instance.logEvent(
                                          name:
                                              'competitions_view_all_dashboard',
                                          parameters: {
                                            "competitions_button": 'click',
                                          });

                                      if (Preference.getString(Preference.ROLE)
                                                  ?.toLowerCase() ==
                                              'learner' ||
                                          Preference.getString(Preference.ROLE)
                                                  ?.toLowerCase() ==
                                              'lead') {
                                        menuProvider?.updateCurrentIndex(
                                            '/g-competitions');
                                      } else {
                                        Navigator.of(context)
                                            .push(MaterialPageRoute(
                                                builder: (context) =>
                                                    EventListingPage()))
                                            .then((value) {
                                          setState(() {
                                            getCompetitionList();
                                          });
                                        });
                                      }
                                    },
                                    child: Padding(
                                      padding:
                                          const EdgeInsets.only(right: 16.0),
                                      child: Text('view_all',
                                          style: Styles.regular(
                                            size: 12,
                                            color: ColorConstants.BODY_TEXT,
                                          )).tr(),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 6),
                            ],
                          ),
                        ),
                      ),

                    if (isEnableEvent == true) ...[
                      Divider(
                          thickness: 1, color: ColorConstants.DIVIDER_COLOR_1)
                    ],
                    if (competitionResponse?.event != null) ...[
                      Container(
                        child: Padding(
                          padding: isEnableEvent == true
                              ? EdgeInsets.symmetric(horizontal: 8, vertical: 4)
                              : EdgeInsets.symmetric(
                                  horizontal: 0, vertical: 0),
                          child: competitionResponse?.event?.length != null
                              ? Column(
                                  children: [
                                    ListView.builder(
                                        itemCount: (competitionResponse
                                                    ?.event?.length)! <
                                                4
                                            ? competitionResponse?.event?.length
                                            : 4,
                                        shrinkWrap: true,
                                        physics: NeverScrollableScrollPhysics(),
                                        itemBuilder:
                                            (BuildContext context, int index) {
                                          if (competitionResponse?.event![index]
                                                  ?.progressStatus
                                                  .toString()
                                                  .toLowerCase() !=
                                              'past') {
                                            isEnableEvent = true;
                                          }

                                          return competitionResponse
                                                      ?.event![index]
                                                      ?.progressStatus
                                                      .toString()
                                                      .toLowerCase() !=
                                                  'past'
                                              ? InkWell(
                                                  onTap: () {
                                                    FirebaseAnalytics.instance
                                                        .logEvent(
                                                            name:
                                                                'event_dashboard',
                                                            parameters: {
                                                          "event_name":
                                                              competitionResponse
                                                                      ?.event![
                                                                          index]
                                                                      ?.name ??
                                                                  '',
                                                        });
                                                    Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                            builder: (BuildContext
                                                                    context) =>
                                                                CompetitionDetail(
                                                                    competitionId: competitionResponse
                                                                        ?.event?[
                                                                            index]
                                                                        ?.id,
                                                                    isEvent:
                                                                        true)));
                                                  },
                                                  child: renderCompetitionCard(
                                                    '${competitionResponse?.event![index]?.image ?? ''}',
                                                    '${competitionResponse?.event![index]?.name ?? ''}',
                                                    '${competitionResponse?.event![index]?.organizedBy ?? ''}',
                                                    '${competitionResponse?.event![index]?.competitionLevel ?? tr('easy')}',
                                                    '${competitionResponse?.event![index]?.gScore}',
                                                    '${competitionResponse?.event![index]?.startDate}',
                                                    '${competitionResponse?.event![index]?.endDate}',
                                                    true,
                                                    '${competitionResponse?.event![index]?.progressStatus}',
                                                    '${competitionResponse?.event![index]?.location}',
                                                  ))
                                              : SizedBox();
                                        }),
                                  ],
                                )
                              : CompetitionBlankPage(),
                        ),
                      ),
                      // SizedBox(
                      //   height: 20,
                      // ),
                    ],

                    // competitionResponse?.data?.length != null
                    //     ? Center(
                    //         child: CustomOutlineButton(
                    //           strokeWidth: 1,
                    //           radius: 50,
                    //           gradient: LinearGradient(
                    //             colors: [
                    //               ColorConstants().gradientLeft(),
                    //               ColorConstants().gradientRight()
                    //             ],
                    //             begin: Alignment.topLeft,
                    //             end: Alignment.topRight,
                    //           ),
                    //           child: Padding(
                    //             padding: const EdgeInsets.only(left: 50.0, right: 50.0),
                    //             child: GradientText(
                    //               '${tr('all_competition')}',
                    //               style: Styles.regular(size: 14),
                    //               colors: [
                    //                 ColorConstants().gradientLeft(),
                    //                 ColorConstants().gradientRight(),
                    //               ],
                    //             ),
                    //           ),
                    //           onPressed: () {
                    //             FirebaseAnalytics.instance.logEvent(
                    //                 name: 'competitions_view_all_dashboard',
                    //                 parameters: {
                    //                   "competitions_button": 'click',
                    //                 });
                    //             menuProvider?.updateCurrentIndex('/g-competitions');
                    //           },
                    //         ),
                    //       )
                    //     : SizedBox(),
                    // competitionResponse?.data?.length != null
                    //     ? SizedBox(
                    //         height: 20,
                    //       )
                    //     : SizedBox(),
                    // SizedBox(
                    //   height: 10,
                    // ),
                  ],
                ),
              )
            : SizedBox(),

        //TODO: Competition
        if (Preference.getString(Preference.ROLE)?.toLowerCase() == 'learner' ||
            Preference.getString(Preference.ROLE)?.toLowerCase() == 'lead')
          competitionResponse?.data != null &&
                  competitionResponse?.data?.length != 0
              ? Container(
                  margin: EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(color: ColorConstants.WHITE),
                  padding: const EdgeInsets.only(top: 16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Icon(Icons.emoji_events_outlined, size: 18),
                                  SizedBox(width: 10),
                                  Text(
                                    'competitions',
                                    style: Styles.bold(
                                        color: ColorConstants.HEADING_TITLE,
                                        size: 14),
                                  ).tr(),
                                  Spacer(),
                                  InkWell(
                                    onTap: () {
                                      FirebaseAnalytics.instance.logEvent(
                                          name:
                                              'competitions_view_all_dashboard',
                                          parameters: {
                                            "competitions_button": 'click',
                                          });
                                      menuProvider?.updateCurrentIndex(
                                          '/g-competitions');
                                    },
                                    child: Padding(
                                      padding:
                                          const EdgeInsets.only(right: 16.0),
                                      child: Text('view_all',
                                          style: Styles.regular(
                                            size: 12,
                                            color: ColorConstants.BODY_TEXT,
                                          )).tr(),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 6),
                      Divider(
                          thickness: 1, color: ColorConstants.DIVIDER_COLOR_1),

                      Container(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          child: competitionResponse?.data?.length != null
                              ? Column(
                                  children: [
                                    ListView.builder(
                                        itemCount: (competitionResponse
                                                    ?.data?.length)! <
                                                4
                                            ? competitionResponse?.data?.length
                                            : 4,
                                        shrinkWrap: true,
                                        physics: NeverScrollableScrollPhysics(),
                                        itemBuilder:
                                            (BuildContext context, int index) {
                                          String startDate =
                                              '${competitionResponse?.data?[index]?.startDate?.split(' ').first}';

                                          DateTime dateTime =
                                              DateTime.parse(startDate);

                                          String endDate =
                                              '${competitionResponse?.data?[index]?.endDate?.split(' ').first}';
                                          dateTime = DateTime.parse(endDate);

                                          return InkWell(
                                              onTap: () {
                                                FirebaseAnalytics.instance.logEvent(
                                                    name:
                                                        'competitions_dashboard',
                                                    parameters: {
                                                      "competitions_name":
                                                          competitionResponse
                                                                  ?.data![index]
                                                                  ?.name ??
                                                              '',
                                                    });
                                                Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                        builder: (BuildContext
                                                                context) =>
                                                            CompetitionDetail(
                                                                competitionId:
                                                                    competitionResponse
                                                                        ?.data?[
                                                                            index]
                                                                        ?.id)));
                                              },
                                              child: renderCompetitionCard(
                                                '${competitionResponse?.data![index]?.image ?? ''}',
                                                '${competitionResponse?.data![index]?.name ?? ''}',
                                                '${competitionResponse?.data![index]?.organizedBy ?? ''}',
                                                '${competitionResponse?.data![index]?.competitionLevel ?? tr('easy')}',
                                                '${competitionResponse?.data![index]?.gScore}',
                                                '${competitionResponse?.data![index]?.startDate}',
                                                '${competitionResponse?.data![index]?.endDate}',
                                                false,
                                                '${competitionResponse?.data![index]?.progressStatus}',
                                                '${competitionResponse?.data![index]?.location}',
                                              ));
                                        }),
                                  ],
                                )
                              : CompetitionBlankPage(),
                        ),
                      ),
                      // competitionResponse?.data?.length != null
                      //     ? Center(
                      //         child: CustomOutlineButton(
                      //           strokeWidth: 1,
                      //           radius: 50,
                      //           gradient: LinearGradient(
                      //             colors: [
                      //               ColorConstants().gradientLeft(),
                      //               ColorConstants().gradientRight()
                      //             ],
                      //             begin: Alignment.topLeft,
                      //             end: Alignment.topRight,
                      //           ),
                      //           child: Padding(
                      //             padding: const EdgeInsets.only(left: 50.0, right: 50.0),
                      //             child: GradientText(
                      //               '${tr('all_competition')}',
                      //               style: Styles.regular(size: 14),
                      //               colors: [
                      //                 ColorConstants().gradientLeft(),
                      //                 ColorConstants().gradientRight(),
                      //               ],
                      //             ),
                      //           ),
                      //           onPressed: () {
                      //             FirebaseAnalytics.instance.logEvent(
                      //                 name: 'competitions_view_all_dashboard',
                      //                 parameters: {
                      //                   "competitions_button": 'click',
                      //                 });
                      //             menuProvider?.updateCurrentIndex('/g-competitions');
                      //           },
                      //         ),
                      //       )
                      //     : SizedBox(),
                      // competitionResponse?.data?.length != null
                      //     ? SizedBox(
                      //         height: 20,
                      //       )
                      //     : SizedBox(),
                    ],
                  ),
                )
              : SizedBox(),
      ],
    );
  }

  //TODO: Add New Job  Dashboard 11-Aug-2023
  jobDashboardWidgets() {
    return Container(
      decoration: BoxDecoration(color: ColorConstants.WHITE),
      child: Column(
        children: [
          SizedBox(
            height: height(context) * 0.085,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: Utility().isRTL(context)
                    ? EdgeInsets.only(right: 15.0)
                    : EdgeInsets.only(left: 15.0),
                child: SvgPicture.asset('assets/images/s_careers.svg'),
              ),
              Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 10,
                  ),
                  child: Text(
                    'Oman Jobs',
                    style: Styles.bold(color: Color(0xff0E1638)),
                  ).tr()),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: SizedBox(
              height: 80,
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  jobDetailWidget(
                      "Domains",
                      dashboardContentResponse?.data?.jobDashboard?[0].domains,
                      15),
                  jobDetailWidget(
                      "Companies",
                      dashboardContentResponse
                          ?.data?.jobDashboard?[0].companies,
                      25),
                  jobDetailWidget(
                      "JobPosting",
                      dashboardContentResponse
                          ?.data?.jobDashboard?[0].jobPosting,
                      15),
                  jobDetailWidget(
                      "Job Roles",
                      dashboardContentResponse?.data?.jobDashboard?[0].jobRoles,
                      25),
                  jobDetailWidget(
                      "Location",
                      dashboardContentResponse?.data?.jobDashboard?[0].location,
                      15),
                  jobDetailWidget(
                      "Vacancies",
                      dashboardContentResponse
                          ?.data?.jobDashboard?[0].vacancies,
                      25),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget jobDetailWidget(String name, dynamic value, dynamic percent) {
    return InkWell(
      onTap: () {},
      child: Container(
          width: min(width(context), 480) * 0.44,
          decoration: BoxDecoration(
            color: ColorConstants().gradientRight().withValues(alpha: 0.08),
            borderRadius: BorderRadius.circular(10),
          ),
          margin: EdgeInsets.all(8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text("$name"),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "$value",
                    style: Styles.regular(),
                  ),
                  SizedBox(
                    width: 4,
                  ),
                  Text(
                    "$percent%",
                    style: Styles.regular(
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(
                    width: 4,
                  ),
                  Transform.translate(
                      offset: Offset(Utility().isRTL(context) ? 10.0 : 0, 0),
                      child: Icon(
                        Icons.arrow_drop_up_outlined,
                        color: Colors.green,
                        size: 20,
                      )),
                ],
              ),
            ],
          )),
    );
  }

  //TODO:Event and Competition Widgets
  renderCompetitionCard(
      String competitionImg,
      String name,
      String companyName,
      String difficulty,
      String gScore,
      String startdate,
      String endDate,
      bool enablePStatus,
      String progressStatus,
      String? location) {
    return Stack(
      children: [
        Container(
          height: enablePStatus == true ? 110 : 122,
          width: double.infinity,
          padding: EdgeInsets.all(8),
          margin: EdgeInsets.symmetric(vertical: 7),
          decoration: BoxDecoration(
            color: ColorConstants.WHITE,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: ColorConstants.DIVIDER_COLOR_1, width: 1),
          ),
          child: Row(children: [
            Container(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: CachedNetworkImage(
                  imageUrl: competitionImg,
                  width: enablePStatus == true ? 90 : 95,
                  height: enablePStatus == true ? 90 : 90,
                  errorWidget: (context, url, error) => SvgPicture.asset(
                    'assets/images/event_default.svg',
                    height: 10,
                    width: 10,
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(width: 10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              //mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                SizedBox(
                  width: width(context) * 0.5,
                  child: Text(
                    name,
                    style: Styles.bold(size: 16),
                    maxLines: 1,
                    softWrap: true,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                companyName.isNotEmpty
                    ? SizedBox(
                        width: MediaQuery.of(context).size.width * 0.62,
                        child: Text(
                          companyName,
                          maxLines: 1,
                          style: Styles.semibold(
                              size: 11, color: ColorConstants.GREY_3),
                        ),
                      )
                    : SizedBox(),
                SizedBox(height: 2),

                //TODO: Show Live
                enablePStatus == true
                    ? progressStatus.toLowerCase() == 'live'
                        ? Row(
                            children: [
                              SvgPicture.asset(
                                'assets/images/live_icon.svg',
                                fit: BoxFit.fitHeight,
                              ),
                              SizedBox(width: 3),
                              progressStatus.toLowerCase() == 'live'
                                  ? FadeTransition(
                                      opacity: _animation,
                                      child: Text(
                                        '${'live'}',
                                        style: Styles.bold(
                                            color: ColorConstants.RED,
                                            size: 16),
                                      ).tr(),
                                    )
                                  : Text('$progressStatus',
                                      style: Styles.bold(
                                          color: ColorConstants.RED, size: 16))
                            ],
                          )
                        : SizedBox()
                    : SizedBox(),

                // TODO: Show Location
                SizedBox(height: 3),
                if (location != 'null' &&
                    location != '' &&
                    isEnableEvent == true)
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.location_on,
                        color: ColorConstants.BODY_TEXT,
                        size: 16,
                      ),
                      SizedBox(
                        width: 3,
                      ),
                      Text('${location ?? ''}',
                          style: Styles.regular(
                              size: 12,
                              // lineHeight: 1,
                              color: ColorConstants.BODY_TEXT)),
                    ],
                  ),

                enablePStatus == true
                    ? SizedBox()
                    : Padding(
                        padding: const EdgeInsets.only(top: 3.0),
                        child: Row(
                          children: [
                            Text('${difficulty.capital()}',
                                style: Styles.regular(
                                    color: ColorConstants.GREEN_1, size: 12)),
                            SizedBox(
                              width: 4,
                            ),
                            Text('•',
                                style: Styles.regular(
                                    color: ColorConstants.GREY_2, size: 12)),
                            SizedBox(
                              width: 4,
                            ),
                            SizedBox(
                                height: 15,
                                child: Image.asset('assets/images/coin.png')),
                            SizedBox(
                              width: 4,
                            ),
                            Text('$gScore ${tr('points')}',
                                style: Styles.regular(
                                    color: ColorConstants.ORANGE_4, size: 12)),
                          ],
                        ),
                      ),
                enablePStatus == true && progressStatus.toLowerCase() == 'live'
                    ? SizedBox()
                    : Padding(
                        padding: const EdgeInsets.only(top: 2.0),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.calendar_month,
                              color: ColorConstants.BODY_TEXT,
                              size: 18,
                            ),
                            SizedBox(
                              width: 4,
                            ),

                            //TODO: Show start date Time
                            StrToTime(
                              time: startdate,
                              dateFormat: ' dd-MMM-yy ',
                              //appendString: Utility().isRTL(context) ? '' : tr('to'),
                              appendString: ',',
                              textStyle: Styles.regular(
                                  size: 12, color: Color(0xff5A5F73)),
                            ),

                            StrToTime(
                              time: startdate,
                              dateFormat: ' hh:mm a ',
                              //appendString: Utility().isRTL(context) ? '' : tr('to'),
                              appendString: '',
                              textStyle: Styles.regular(
                                  size: 12,
                                  lineHeight: 1,
                                  color: ColorConstants.BODY_TEXT),
                            ),
                          ],
                        ),
                      ),
                enablePStatus == true && progressStatus.toLowerCase() == 'live'
                    ? SizedBox()
                    : Padding(
                        padding: const EdgeInsets.only(top: 5.0),
                        child: Row(
                          children: [
                            /*Icon(
                              Icons.access_time,
                              color: ColorConstants.BODY_TEXT,
                              size: 16,
                            ),*/
                            Text(
                              'To',
                              style: Styles.bold(
                                  size: 13,
                                  lineHeight: 1,
                                  color: ColorConstants.BODY_TEXT),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            //TODO: Show end date time
                            StrToTime(
                              time: endDate,
                              dateFormat: ' dd-MMM-yy ',
                              //appendString: Utility().isRTL(context) ? tr('to') : '',
                              appendString: ',',
                              textStyle: Styles.regular(
                                  size: 12, color: Color(0xff5A5F73)),
                            ),

                            StrToTime(
                              time: endDate,
                              dateFormat: ' hh:mm aa ',
                              appendString:
                                  Utility().isRTL(context) ? tr('to') : '',
                              textStyle: Styles.regular(
                                  size: 12,
                                  lineHeight: 1,
                                  color: Color.fromARGB(255, 4, 6, 16)),
                            ),
                          ],
                        ),
                      ),
              ],
            ),
          ]),
        ),
        Positioned(
            right: Utility().isRTL(context) ? null : 8,
            left: Utility().isRTL(context) ? 8 : null,
            top: 10,
            bottom: 10,
            child: Icon(
              (Icons.arrow_forward_ios),
              size: 20,
            )),
      ],
    );
  }

  renderWidgets(pages) {
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("getDashboardIsVisible") == null) {
            return BlankPage();
          } else if (box.get("getDashboardIsVisible").isEmpty) {
            return SizedBox();
          }

          dynamic content = box.get("getDashboardIsVisible") as Map;
          List<Widget> list = <Widget>[];
          /*list.add(SizedBox(
            height: height(context) * 0.12,
          ));*/
          content.forEach((key, value) {
            if (value != "0" && pages[key] != null)
              list.add(pages[key] as Widget);
          });

          return Container(
            child: Column(
              children: list,
            ),
          );
        });
  }

  renderSession() {
    List<String> months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("dashboard_sessions_limit") == null) {
            return SizedBox();
          } else if (box.get("dashboard_sessions_limit").isEmpty) {
            return SizedBox();
          }

          sessionList = box
              .get("dashboard_sessions_limit")
              .map((e) =>
                  DashboardSessionsLimit.fromJson(Map<String, dynamic>.from(e)))
              .cast<DashboardSessionsLimit>()
              .toList();

          return Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 10,
                      ),
                      child: Text(
                        'today_classes',
                        style: Styles.bold(color: Color(0xff0E1638)),
                      ).tr()),
                  Expanded(child: SizedBox()),
                ],
              ),
              Container(
                  padding: EdgeInsets.all(10),
                  decoration: BoxDecoration(color: ColorConstants.GREY),
                  child: ListView.builder(
                    itemBuilder: (BuildContext context, int index) {
                      return sessionList!.length > 0
                          ? Container(
                              padding: EdgeInsets.all(10),
                              margin: EdgeInsets.symmetric(vertical: 10),
                              decoration: BoxDecoration(
                                  color: ColorConstants.WHITE,
                                  border: Border.all(
                                      color: Colors.grey[350]!, width: 1),
                                  borderRadius: BorderRadius.circular(10)),
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    sessionList![index]
                                                .liveclassStatus!
                                                .toLowerCase() ==
                                            'live'
                                        ? Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              sessionList![index]
                                                          .contentType!
                                                          .toLowerCase() !=
                                                      'offlineclass'
                                                  ? SvgPicture.asset(
                                                      'assets/images/live_icon.svg',
                                                      width: 25,
                                                      height: 25,
                                                      allowDrawingOutsideViewBox:
                                                          true,
                                                    )
                                                  : SvgPicture.asset(
                                                      'assets/images/offline_live.svg',
                                                      allowDrawingOutsideViewBox:
                                                          true,
                                                    ),
                                              SizedBox(width: 5),
                                              Text(
                                                      sessionList![index]
                                                                  .contentType!
                                                                  .toLowerCase() ==
                                                              'offlineclass'
                                                          ? 'ongoing'
                                                          : 'Live_now',
                                                      style: Styles.regular(
                                                          size: 12,
                                                          color: ColorConstants()
                                                                  .primaryColor() ??
                                                              ColorConstants()
                                                                  .primaryColorAlways()))
                                                  .tr(),
                                              Expanded(child: SizedBox()),
                                              Container(
                                                decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                    color:
                                                        ColorConstants.BG_GREY),
                                                padding: EdgeInsets.symmetric(
                                                    vertical: 8,
                                                    horizontal: 18),
                                                child: Text(
                                                        sessionList![index]
                                                                    .contentType!
                                                                    .toLowerCase() ==
                                                                'otherclass'
                                                            ? 'weblink'
                                                            : sessionList![index]
                                                                        .contentType!
                                                                        .toLowerCase() ==
                                                                    'teamsclass'
                                                                ? "teams"
                                                                : sessionList![index].contentType!.toLowerCase() ==
                                                                            'liveclass' ||
                                                                        sessionList![index].contentType!.toLowerCase() ==
                                                                            'zoomclass'
                                                                    ? "live"
                                                                    : 'classroom',
                                                        style: Styles.regular(
                                                            size: 10,
                                                            color:
                                                                ColorConstants
                                                                    .BLACK))
                                                    .tr(),
                                              ),
                                            ],
                                          )
                                        : sessionList![index]
                                                    .liveclassStatus!
                                                    .toLowerCase() ==
                                                'upcoming'
                                            ? Row(children: [
                                                SvgPicture.asset(
                                                  'assets/images/upcoming_live.svg',
                                                  allowDrawingOutsideViewBox:
                                                      true,
                                                ),
                                                SizedBox(width: 5),
                                                CallOnceWidget(
                                                  onCallOnce: () {
                                                    print('page cllaed oned');
                                                  },
                                                  child: StrToTime(
                                                    appendString:
                                                        Utility().isRTL(context)
                                                            ? ''
                                                            : ' - ',
                                                    dateFormat: 'hh:mm a',
                                                    time:
                                                        '${sessionList![index].startTime}',
                                                    textStyle: Styles.regular(
                                                        size: 14),
                                                  ),
                                                ),
                                                CallOnceWidget(
                                                  onCallOnce: () {
                                                    print('page cllaed oned');
                                                  },
                                                  child: StrToTime(
                                                    appendString:
                                                        Utility().isRTL(context)
                                                            ? ' - '
                                                            : '',
                                                    dateFormat: 'hh:mm a',
                                                    time:
                                                        '${sessionList![index].endTime}',
                                                    textStyle: Styles.regular(
                                                        size: 14),
                                                  ),
                                                ),
                                                Text(
                                                  ' ${Utility().isRTL(context) ? '' : '|'} ${DateFormat('d').format(DateTime.fromMillisecondsSinceEpoch(sessionList![index].fromDate! * 1000))} ${months[int.parse(DateFormat('M').format(DateTime.fromMillisecondsSinceEpoch(sessionList![index].fromDate! * 1000))) - 1]} ${Utility().isRTL(context) ? '|' : ''}',
                                                  style:
                                                      Styles.regular(size: 14),
                                                  textDirection:
                                                      ui.TextDirection.ltr,
                                                ),
                                                // Text(
                                                //     '${sessionList![index].startTime} - ${sessionList![index].endTime} |${DateFormat('d').format(DateTime.fromMillisecondsSinceEpoch(sessionList![index].fromDate! * 1000))} ${months[int.parse(DateFormat('M').format(DateTime.fromMillisecondsSinceEpoch(sessionList![index].fromDate! * 1000))) - 1]}',
                                                //     style: Styles.regular(
                                                //         size: 12),
                                                //          textDirection: ui.TextDirection.ltr,
                                                //         ),
                                                Expanded(child: SizedBox()),
                                                Container(
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10),
                                                      color: ColorConstants
                                                          .BG_GREY),
                                                  padding: EdgeInsets.symmetric(
                                                      vertical: 8,
                                                      horizontal: 18),
                                                  child: Text(
                                                          sessionList![index]
                                                                      .contentType!
                                                                      .toLowerCase() ==
                                                                  'otherclass'
                                                              ? 'weblink'
                                                              : sessionList![index]
                                                                          .contentType!
                                                                          .toLowerCase() ==
                                                                      'teamsclass'
                                                                  ? "teams"
                                                                  : sessionList![index]
                                                                              .contentType!
                                                                              .toLowerCase() ==
                                                                          'offlineclass'
                                                                      ? 'classroom'
                                                                      : "live",
                                                          style: Styles.regular(
                                                              size: 10,
                                                              color:
                                                                  ColorConstants
                                                                      .BLACK))
                                                      .tr(),
                                                ),
                                              ])
                                            : SizedBox(),
                                    SizedBox(height: 10),
                                    Text('${sessionList![index].name}',
                                        style: Styles.semibold(size: 16)),
                                    SizedBox(height: 9),
                                    Text(
                                      '${sessionList![index].description}',
                                      style: Styles.regular(size: 14),
                                    ),
                                    SizedBox(height: 15),
                                    Row(
                                      children: [
                                        sessionList![index].presenterName !=
                                                    null &&
                                                sessionList![index]
                                                        .presenterName !=
                                                    ''
                                            ? Text(
                                                '${tr('by')} ${Utility().decrypted128('${sessionList![index].presenterName}')} ',
                                                style: Styles.regular(size: 12))
                                            : Text(''),
                                        Expanded(child: SizedBox()),
                                        sessionList![index]
                                                        .liveclassStatus!
                                                        .toLowerCase() ==
                                                    'live' ||
                                                (currentIndiaTime!
                                                                .add(Duration(
                                                                    minutes:
                                                                        15))
                                                                .millisecondsSinceEpoch ~/
                                                            1000 >=
                                                        sessionList![index]
                                                            .fromDate! &&
                                                    currentIndiaTime!
                                                                .millisecondsSinceEpoch ~/
                                                            1000 <
                                                        sessionList![index]
                                                            .endDate!)
                                            ? InkWell(
                                                onTap: () {
                                                  if (sessionList![index]
                                                          .contentType!
                                                          .toLowerCase() ==
                                                      "offlineclass") return;
                                                  if (sessionList![index]
                                                              .contentType!
                                                              .toLowerCase() ==
                                                          "zoomclass" ||
                                                      sessionList![index]
                                                              .contentType!
                                                              .toLowerCase() ==
                                                          'teamsclass' ||
                                                      sessionList![index]
                                                              .contentType!
                                                              .toLowerCase() ==
                                                          'otherclass') {
                                                    setState(() {
                                                      currentZoomUrl =
                                                          sessionList![index]
                                                              .zoomUrl;
                                                      currentOpenUrl =
                                                          sessionList![index]
                                                              .openUrl;
                                                    });

                                                    if (currentZoomUrl !=
                                                        null) {
                                                      BlocProvider.of<HomeBloc>(
                                                              context)
                                                          .add(ZoomOpenUrlEvent(
                                                        contentId:
                                                            sessionList![index]
                                                                .id,
                                                      ));
                                                      launchUrl(
                                                          Uri.parse(
                                                              '$currentZoomUrl'),
                                                          mode: LaunchMode
                                                              .externalApplication);
                                                    } else {
                                                      BlocProvider.of<HomeBloc>(
                                                              context)
                                                          .add(ZoomOpenUrlEvent(
                                                              contentId:
                                                                  sessionList![
                                                                          index]
                                                                      .id));
                                                    }
                                                  } else {
                                                    ScaffoldMessenger.of(
                                                            context)
                                                        .showSnackBar(SnackBar(
                                                      content:
                                                          Text("coming_soon")
                                                              .tr(),
                                                    ));
                                                  }
                                                },
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    gradient: LinearGradient(
                                                        colors: sessionList![index].contentType!.toLowerCase() == "liveclass" ||
                                                                sessionList![
                                                                            index]
                                                                        .contentType!
                                                                        .toLowerCase() ==
                                                                    "zoomclass" ||
                                                                sessionList![
                                                                            index]
                                                                        .contentType!
                                                                        .toLowerCase() ==
                                                                    'teamsclass' ||
                                                                sessionList![
                                                                            index]
                                                                        .contentType!
                                                                        .toLowerCase() ==
                                                                    'otherclass'
                                                            ? [
                                                                ColorConstants()
                                                                    .gradientLeft(),
                                                                ColorConstants()
                                                                    .gradientRight(),
                                                              ]
                                                            : [
                                                                sessionList![index]
                                                                            .contentType!
                                                                            .toLowerCase() ==
                                                                        "offlineclass"
                                                                    ? ColorConstants
                                                                        .WHITE
                                                                    : ColorConstants
                                                                        .GREY_3,
                                                                sessionList![index]
                                                                            .contentType!
                                                                            .toLowerCase() ==
                                                                        "offlineclass"
                                                                    ? ColorConstants
                                                                        .WHITE
                                                                    : ColorConstants
                                                                        .GREY_3,
                                                              ]),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            8),
                                                  ),
                                                  child: Padding(
                                                      child: Text(
                                                              sessionList![index].contentType!.toLowerCase() == "liveclass" ||
                                                                      sessionList![index].contentType!.toLowerCase() ==
                                                                          "zoomclass" ||
                                                                      sessionList![index].contentType!.toLowerCase() ==
                                                                          'teamsclass' ||
                                                                      sessionList![index].contentType!.toLowerCase() ==
                                                                          'otherclass'
                                                                  ? 'join_now'
                                                                  : 'in_progress',
                                                              style: Styles.regular(
                                                                  size: 12,
                                                                  color: sessionList![index].contentType!.toLowerCase() == "offlineclass"
                                                                      ? ColorConstants
                                                                          .BLACK
                                                                      : ColorConstants()
                                                                          .primaryForgroundColor()))
                                                          .tr(),
                                                      padding: EdgeInsets.symmetric(
                                                          horizontal: sessionList![index].contentType!.toLowerCase() ==
                                                                  "offlineclass"
                                                              ? 4
                                                              : 18,
                                                          vertical: 8)),
                                                ))
                                            : Text('upcoming',
                                                    style: Styles.regular(
                                                        size: 12))
                                                .tr(),
                                        Visibility(
                                            child: Padding(
                                                child: Text(
                                                  'concluded',
                                                  style: Styles.regular(
                                                      size: 12,
                                                      color:
                                                          ColorConstants.BLACK),
                                                ).tr(),
                                                padding: EdgeInsets.all(10)),
                                            visible: sessionList![index]
                                                    .liveclassStatus!
                                                    .toLowerCase() ==
                                                'completed')
                                      ],
                                    )
                                  ]))
                          : Container(child: Text(""));
                    },
                    itemCount: sessionList?.length,
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                  ))
            ],
          );
        });
  }

  renderRecenetActivites() {
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("recent_activity") == null) {
            //if (box.get("to_do_activities") == null) {
            return BlankPage();
          } else if (box.get("recent_activity").isEmpty) {
            //} else if (box.get("to_do_activities").isEmpty) {
            return SizedBox();
          }

          recentActivity = box
              .get("recent_activity")
              .map((e) => RecentActivity.fromJson(Map<String, dynamic>.from(e)))
              .cast<RecentActivity>()
              .toList();

          /*recentActivity = box
              .get("to_do_activities")
              .map((e) => RecentActivity.fromJson(Map<String, dynamic>.from(e)))
              .cast<RecentActivity>()
              .toList();*/

          return Container(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                      left: 10, right: 10, top: 16, bottom: 2),
                  child: Padding(
                    padding: EdgeInsets.only(
                      left: 6.0,
                      right: 6.0,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'recent_activities',
                          style: Styles.bold(
                            color: Color(0xff0E1638),
                          ),
                        ).tr(),
                        Expanded(child: SizedBox()),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (BuildContext context, int index) {
                      return recentActivity?[index].contentType == 'assignment'
                          ? assignmentWidget(recentActivity?[index])
                          : assessmentWidget(recentActivity?[index], index);
                    },
                    itemCount: recentActivity?.length,
                  ),
                )
              ],
            ),
          );
        });
  }

  assignmentWidget(RecentActivity? item) {
    return InkWell(
        onTap: () {
          Navigator.push(
            context,
            NextPageRoute(
                ChangeNotifierProvider<MgAssignmentDetailProvider>(
                    create: (c) => MgAssignmentDetailProvider(
                        TrainingService(ApiService()),
                        AssignmentList(
                          contentId: item?.contentId,
                          title: item?.title,
                          description: item?.description,
                          startDate: item?.startDate,
                          endDate: item?.endDate,
                          allowMultiple: item?.allowMultiple,
                          isGraded: item?.isGraded,
                          submissionMode: item?.submissionMode,
                          maximumMarks: item?.maximumMarks,
                          contentType: item?.contentType,
                          languageId: item?.languageId,
                          moduleId: item?.moduleId,
                          totalAttempts: item?.totalAttempts,
                          score: item?.score,
                          status: item?.status,
                          file: item?.file,
                        )),
                    child: MgAssignmentDetailPage(
                      id: item?.contentId,
                      status: item?.status?.toLowerCase(),
                    )),
                isMaintainState: true),
          );
        },
        child: Container(
            padding: EdgeInsets.only(left: 10, right: 10, top: 17, bottom: 17),
            width: MediaQuery.of(context).size.width * 0.9,
            margin: EdgeInsets.symmetric(vertical: 10, horizontal: 6),
            decoration: BoxDecoration(
              color: ColorConstants.WHITE,
              borderRadius: BorderRadius.circular(10),
              /*border: Border.all(
                    color: Colors.green, width: 1, style: BorderStyle.solid)*/
            ),
            child:
                Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
              if (Utility.isExpired(item?.endDate, currentIndiaTime!) &&
                  item?.status != 'Completed') ...[
                SvgPicture.asset(
                  'assets/images/missed_icon.svg',
                  width: 20,
                  height: 20,
                  allowDrawingOutsideViewBox: true,
                ),
              ] else if (item?.status == 'Completed') ...[
                SvgPicture.asset(
                  'assets/images/completed_icon.svg',
                  width: 20,
                  height: 20,
                  allowDrawingOutsideViewBox: true,
                ),
              ] else if (item?.status == 'Upcoming') ...[
                SvgPicture.asset(
                  'assets/images/upcoming_live.svg',
                  width: 20,
                  height: 20,
                  allowDrawingOutsideViewBox: true,
                ),
              ] else if (item?.status == 'Pending') ...[
                SvgPicture.asset(
                  'assets/images/pending_icon.svg',
                  width: 20,
                  height: 20,
                  allowDrawingOutsideViewBox: true,
                ),
              ],
              SizedBox(width: 20),
              Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: Text('${item?.title}',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          softWrap: false,
                          style: Styles.bold(size: 16)),
                    ),
                    SizedBox(height: 5),
                    if (item?.status == 'Completed') ...[
                      Text('submitted', style: Styles.regular(size: 12)).tr(),
                      SizedBox(height: 5),
                      // Text(
                      //     '${DateFormat('MM/dd/yyyy, hh:mm a').format(DateTime.fromMillisecondsSinceEpoch(item.endDate! * 1000).toUtc())}',
                      //     style: Styles.regular(size: 12)),
                      // SizedBox(height: 5),
                    ] else if (item?.status == 'Upcoming') ...[
                      SizedBox(height: 5),
                      Text(
                          '${tr('submission_date')}: ${DateFormat('dd-MMM-yyyy, hh:mm aaa').format(DateTime.fromMillisecondsSinceEpoch(item!.endDate! * 1000))}',
                          style: Styles.regular(
                            size: 12,
                          )),
                      SizedBox(
                        height: 3,
                      ),
                      Text(
                          '${tr('deadline')}: ${DateFormat('dd-MMM-yyyy, hh:mm aaa').format(
                            DateTime.fromMillisecondsSinceEpoch(
                                item.endDate! * 1000),
                          )}',
                          style: Styles.regular(size: 12)),
                      SizedBox(height: 5),
                    ] else if (item?.status == 'Pending') ...[
                      Text('${tr('${item?.status}'.toLowerCase())}',
                          style: Styles.regular(
                              size: 12,
                              color: ColorConstants().primaryColor() ??
                                  ColorConstants().primaryColorAlways())),
                      SizedBox(height: 5),
                      Text(
                          '${tr('submission_date')}: ${DateFormat('dd-MMM-yyyy, hh:mm aaa').format(DateTime.fromMillisecondsSinceEpoch(item!.endDate! * 1000))}',
                          style: Styles.regular(
                            size: 12,
                          )),
                      SizedBox(
                        height: 3,
                      ),
                      Text(
                          '${tr('deadline')}: ${DateFormat('dd-MMM-yyyy, hh:mm aaa').format(DateTime.fromMillisecondsSinceEpoch(item.endDate! * 1000))}',
                          style: Styles.regular(
                            size: 12,
                          )),
                      SizedBox(height: 5),
                    ],
                    if (item?.isGraded == 1 &&
                        item?.score != null &&
                        item?.score != 0)
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                                text: '${tr('score_earned')}: ',
                                style: Styles.regular(size: 12)),
                            TextSpan(
                              text: '${item?.score.toStringAsFixed(2)}',
                              style: Styles.bold(size: 12),
                            ),
                            TextSpan(
                                text: '/${item?.maximumMarks}',
                                style: Styles.regular(size: 12)),
                          ],
                        ),
                      ),
                  ]),
            ])));
  }

  assessmentWidget(RecentActivity? item, int index) {
    assessmentFirstIndex = index;
    return InkWell(
      onTap: () {
        // Log.v('current program data ${item?.toJson()}');
        Navigator.push(
            context,
            NextPageRoute(
                ChangeNotifierProvider<MgAssessmentDetailProvider>(
                    create: (context) => MgAssessmentDetailProvider(
                        TrainingService(ApiService()),
                        AssessmentList(
                            title: item.title,
                            contentId: item.contentId,
                            description: item.description,
                            startDate: item.startDate,
                            endDate: item.endDate,
                            maximumMarks: item.maximumMarks,
                            passingMarks: item.passingMarks,
                            questionCount: item.questionCount,
                            attemptAllowed: item.attemptAllowed,
                            durationInMinutes: item.durationInMinutes,
                            attemptCount: item.attemptCount,
                            difficultyLevel: item.difficultyLevel,
                            module: item.module,
                            skill: item.skill,
                            program: item.program,
                            attemptedOn: item.attemptedOn,
                            score: item.score,
                            status: item.status)),
                    child: MgAssessmentDetailPage(
                      programName: '${item.title}',
                    )),
                isMaintainState: true));
      },
      child: Container(
          padding: EdgeInsets.all(10),
          width: MediaQuery.of(context).size.width * 0.9,
          margin: EdgeInsets.symmetric(vertical: 6, horizontal: 8),
          decoration: BoxDecoration(
            color: ColorConstants.WHITE,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Stack(alignment: Alignment.center, children: [
            Row(
              children: [
                Container(
                  child: Row(children: [
                    if (Utility.isExpired(item?.endDate, currentIndiaTime!) &&
                        item?.status != 'Completed') ...[
                      SvgPicture.asset(
                        'assets/images/missed_icon.svg',
                        width: 20,
                        height: 20,
                        allowDrawingOutsideViewBox: true,
                      ),
                    ] else if (item?.status == 'Completed') ...[
                      SvgPicture.asset(
                        'assets/images/completed_icon.svg',
                        width: 20,
                        height: 20,
                        allowDrawingOutsideViewBox: true,
                      ),
                    ] else if (item?.status == 'Upcoming') ...[
                      SvgPicture.asset(
                        'assets/images/upcoming_live.svg',
                        width: 20,
                        height: 20,
                        allowDrawingOutsideViewBox: true,
                      ),
                    ] else if (item?.status == 'Pending') ...[
                      SvgPicture.asset(
                        'assets/images/pending_icon.svg',
                        width: 20,
                        height: 20,
                        allowDrawingOutsideViewBox: true,
                      ),
                    ],
                    SizedBox(width: 20),
                    Container(
                      width: MediaQuery.of(context).size.width * 0.6,
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Row(
                              children: [
                                Flexible(
                                  child: RichText(
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                    strutStyle: StrutStyle(fontSize: 14.0),
                                    text: TextSpan(
                                      style: Styles.bold(
                                          size: 16,
                                          color: ColorConstants.HEADING_TITLE),
                                      text: '${item?.title}',
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            if (item?.status == 'Completed') ...[
                              SizedBox(height: 5),
                              Text(
                                  '${item?.displayScorecard == 1 ? '${item?.score.toStringAsFixed(2)}/${item?.maximumMarks} ${tr('marks')} • ' : ''}${item?.attemptAllowed == 0 ? 'Unlimited attempts' : '${item!.attemptAllowed! - item.attemptCount!} ${tr('attempts_left')}'}',
                                  style: Styles.regular(
                                      size: 12,
                                      color: ColorConstants.BODY_TEXT)),
                              SizedBox(height: 5),
                              Text(
                                  '${tr('submit_before')}: ${DateFormat('MM/dd/yyyy').format(DateTime.fromMillisecondsSinceEpoch(item!.endDate! * 1000))}',
                                  style: Styles.regular(
                                      size: 12,
                                      color: ColorConstants.BODY_TEXT))
                            ] else ...[
                              SizedBox(height: 5),
                              Text(
                                  '${item?.durationInMinutes} ${tr('minute')} • ${item?.maximumMarks} ${tr('marks')}',
                                  style: Styles.regular(
                                      size: 12,
                                      color: ColorConstants.BODY_TEXT)),
                              SizedBox(height: 5),
                              // Text('${item.endDate}'),
                              Text(
                                  '${tr('submit_before')}: ${DateFormat('MM/dd/yyyy').format(DateTime.fromMillisecondsSinceEpoch(item!.endDate! * 1000))}',
                                  style: Styles.regular(
                                      size: 12,
                                      color: ColorConstants.BODY_TEXT)),
                            ],
                          ]),
                    ),
                  ]),
                ),
              ],
            ),
            Positioned(
                right: Utility().isRTL(context) ? null : 0,
                left: Utility().isRTL(context) ? 0 : null,
                // top: 100,
                // bottom: 100,
                child: Visibility(
                  visible: item.status == 'Completed',
                  child: Align(
                    alignment: Alignment.bottomRight,
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                            context,
                            NextPageRoute(AssessmentYourReportPage(
                              contentId: item.contentId,
                              programId: item.program,
                              displayScorecard: item.displayScorecard,
                            )));
                      },
                      child: Text('report',
                              textAlign: TextAlign.right,
                              style: Styles.regular(
                                  size: 12,
                                  color: ColorConstants().primaryColor() ??
                                      ColorConstants().primaryColorAlways()))
                          .tr(),
                    ),
                  ),
                ))
          ])),
    );
  }

  renderMyCourses() {
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("dashboard_my_courses_limit") == null) {
            return BlankPage();
          } else if (box.get("dashboard_my_courses_limit").isEmpty) {
            return SizedBox();
          }

          myCoursesList = box
              .get("dashboard_my_courses_limit")
              .map((e) => DashboardMyCoursesLimit.fromJson(
                  Map<String, dynamic>.from(e)))
              .cast<DashboardMyCoursesLimit>()
              .toList();

          return Container(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 0,
                    horizontal: 10,
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: 8, right: 8.0, bottom: 10.0, top: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          APK_DETAILS["package_name"] == "com.singulariswow.mec"
                              ? 'course_module'
                              : 'my_course',
                          style:
                              Styles.bold(color: Color(0xff0E1638), size: 14),
                        ).tr(),
                        Expanded(child: SizedBox()),
                        InkWell(
                          onTap: () {
                            // menuProvider?.updateCurrentIndex('/g-school');
                            Navigator.push(
                                context,
                                NextPageRoute(MyCourses(
                                  verticalScroll: true,
                                  viewAll: true,
                                )));
                          },
                          child: Text('view_all',
                              style: Styles.regular(
                                size: 12,
                                color: ColorConstants.BODY_TEXT,
                              )).tr(),
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  height: 148,
                  padding: EdgeInsets.only(left: 10.0, top: 0.0, right: 10.0),
                  child: ListView.builder(
                    itemBuilder: (BuildContext context, int index) {
                      return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            InkWell(
                                onTap: () {
                                  Navigator.push(
                                      context,
                                      NextPageRoute(
                                          ChangeNotifierProvider<
                                                  TrainingDetailProvider>(
                                              create: (context) =>
                                                  TrainingDetailProvider(
                                                      TrainingService(
                                                          ApiService()),
                                                      MProgram(
                                                          id: myCoursesList![
                                                                  index]
                                                              .id)),
                                              child: TrainingDetailPage()),
                                          isMaintainState: true));
                                },
                                child: Container(
                                    padding: EdgeInsets.all(10),
                                    margin: EdgeInsets.only(top: 12, right: 10),
                                    width: min(width(context), 480) * 0.8,
                                    //height: MediaQuery.of(context).size.height * 0.13,
                                    height: 100,
                                    decoration: BoxDecoration(
                                        color: ColorConstants.WHITE,
                                        borderRadius:
                                            BorderRadius.circular(10)),
                                    child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              SizedBox(
                                                child: ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                  child: CachedNetworkImage(
                                                    imageUrl:
                                                        '${myCoursesList?[index].image}',
                                                    width: 80,
                                                    height: 80,
                                                    errorWidget:
                                                        (context, url, error) =>
                                                            SvgPicture.asset(
                                                      'assets/images/gscore_postnow_bg.svg',
                                                    ),
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                              ),
                                              Padding(
                                                padding: EdgeInsets.only(
                                                    left:
                                                        Utility().isRTL(context)
                                                            ? 0
                                                            : 10.0,
                                                    right:
                                                        Utility().isRTL(context)
                                                            ? 10
                                                            : 0),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    SizedBox(
                                                      width: min(width(context),
                                                              480) *
                                                          0.5,
                                                      child: Text(
                                                          '${myCoursesList![index].name}',
                                                          maxLines: 1,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                          softWrap: true,
                                                          style: Styles.bold(
                                                              size: 14)),
                                                    ),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              top: 8.0),
                                                      child: Text(
                                                          '${myCoursesList![index].completion.toString()}% ${tr('completed')}',
                                                          style: Styles.regular(
                                                              size: 12)),
                                                    ),
                                                    SizedBox(
                                                      height: 4,
                                                    ),
                                                    Container(
                                                      height: 7,
                                                      width: min(width(context),
                                                              480) *
                                                          0.50,
                                                      decoration: BoxDecoration(
                                                          color: ColorConstants
                                                              .GREY,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      10)),
                                                      child: Stack(
                                                        children: [
                                                          Container(
                                                            height: 7,
                                                            width: min(
                                                                    width(
                                                                        context),
                                                                    480) *
                                                                0.5 *
                                                                (myCoursesList![
                                                                            index]
                                                                        .completion! /
                                                                    100),
                                                            decoration: BoxDecoration(
                                                                color: ColorConstants
                                                                    .PROGESSBAR_TEAL,
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            10)),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),

                                          // ],
                                        ])))
                          ]);
                    },
                    itemCount: myCoursesList?.length,
                    scrollDirection: Axis.horizontal,
                  ),
                )
              ],
            ),
          );
        });
  }

  renderReels() {
    return HiveDataBuilder<DashboardReelsLimit>(
        hiveKey: "dashboard_reels_limit",
        fromJson: (json) => DashboardReelsLimit.fromJson(json),
        loadingWidget: BlankPage(),
        builder: (context, reelsList) {
          return Container(
            margin: EdgeInsets.only(top: 8),
            color: Colors.white,
            child: Column(
              children: [
                Container(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            SvgPicture.asset(
                              'assets/images/animated_images.svg',
                              height: 22,
                              colorFilter: ColorFilter.mode(
                                  ColorConstants.BLACK, BlendMode.srcIn),
                            ),
                            SizedBox(width: 10),
                            Text('latest_reels',
                                    style: Styles.bold(
                                        size: 14,
                                        color: ColorConstants
                                            .HEADING_PRIMARY_COLOR))
                                .tr(),
                            Spacer(),
                            InkWell(
                              onTap: () {
                                FirebaseAnalytics.instance.logEvent(
                                    name: 'latest_reels_dashboard',
                                    parameters: {
                                      "latest_reels_view": 'view all',
                                    });

                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => ReelScreen()));
                              },
                              child: Padding(
                                  padding: const EdgeInsets.only(right: 8.0),
                                  child: Icon(
                                    Icons.arrow_forward_ios,
                                    size: 20,
                                  )),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                Divider(thickness: 1, color: ColorConstants.DIVIDER_COLOR_1),

                // Row(
                //   mainAxisAlignment: MainAxisAlignment.center,
                //   children: [
                //     Padding(
                //         padding: Utility().isRTL(context)
                //             ? EdgeInsets.symmetric(
                //                 vertical: 8,
                //                 horizontal: 16,
                //               )
                //             : EdgeInsets.symmetric(
                //                 vertical: 8,
                //                 horizontal: 13,
                //               ),
                //         child: Text(
                //           'latest_reels',
                //           style: Styles.bold(color: Color(0xff0E1638)),
                //         ).tr()),
                //     Expanded(child: SizedBox()),
                //     IconButton(
                //         onPressed: () {
                //           FirebaseAnalytics.instance.logEvent(
                //               name: 'latest_reels_dashboard',
                //               parameters: {
                //                 "latest_reels_view": 'view all',
                //               });

                //           Navigator.push(
                //               context,
                //               MaterialPageRoute(
                //                   builder: (context) => ReelScreen()));
                //         },
                //         icon: Icon(
                //           Icons.arrow_forward_ios,
                //           size: 20,
                //         ))
                //   ],
                // ),
                Container(
                    height: 250,
                    margin: EdgeInsets.only(
                        left: 7.0, top: 10.0, right: 7.0, bottom: 10.0),
                    child: ListView.builder(
                        itemCount: reelsList.length,
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (BuildContext context, int index) {
                          return Container(
                            height: MediaQuery.of(context).size.height * 0.2,
                            width: min(width(context), 480) * 0.4,
                            margin: EdgeInsets.only(
                                right: 7, left: 7, top: 4, bottom: 4),
                            child: SizedBox(
                                height: 280,
                                width: 180,
                                child: InkWell(
                                    onTap: () {
                                      FirebaseAnalytics.instance.logEvent(
                                          name:
                                              'latest_reels_dashboard_view_list',
                                          parameters: {
                                            "latest_reels":
                                                reelsList[index].id ?? '',
                                          });
                                      Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) => ReelScreen(
                                                    fromDashboard: true,
                                                    scrollTo: index,
                                                  )));
                                    },
                                    child: Stack(
                                      children: [
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          child: CachedNetworkImage(
                                              height: 280,
                                              width: 180,
                                              imageUrl:
                                                  '${reelsList[index].thumbnailUrl}',
                                              imageBuilder: (context,
                                                      imageProvider) =>
                                                  Container(
                                                    decoration: BoxDecoration(
                                                        image: DecorationImage(
                                                      image: imageProvider,
                                                      fit: BoxFit.cover,
                                                    )),
                                                  ),
                                              errorWidget:
                                                  (context, url, error) =>
                                                      CreateThumnail(
                                                        path: reelsList[index]
                                                            .resourcePath,
                                                      )),
                                        ),
                                        Center(
                                          child: SvgPicture.asset(
                                            'assets/images/play.svg',
                                            height: 40.0,
                                            width: 40.0,
                                            allowDrawingOutsideViewBox: true,
                                          ),
                                        ),
                                        Positioned(
                                          bottom: 10,
                                          left: 10,
                                          child: Text(
                                              '${reelsList[index].viewCount} ${tr('views')}',
                                              style: Styles.regular(
                                                  size: 12,
                                                  color: ColorConstants.WHITE)),
                                        ),
                                      ],
                                    ))),
                          );
                        }))
              ],
            ),
          );
        });
  }

  renderRecommandedCourses() {
    if (Preference.getString(Preference.ROLE) == 'Learner')
      return ValueListenableBuilder(
          valueListenable: Hive.box(DB.CONTENT).listenable(),
          builder: (bc, Box box, child) {
            if (box.get("dashboard_recommended_courses_limit") == null) {
              return BlankPage();
            } else if (box.get("dashboard_recommended_courses_limit").isEmpty) {
              return SizedBox();
            }
            recommendedCourseList = box
                .get("dashboard_recommended_courses_limit")
                .map((e) => DashboardRecommendedCoursesLimit.fromJson(
                    Map<String, dynamic>.from(e)))
                .cast<DashboardRecommendedCoursesLimit>()
                .toList();

            return Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(
                      left: 10.0,
                      top: Utility().isRTL(context) ? 10 : 20,
                      right: 10.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                          padding: Utility().isRTL(context)
                              ? EdgeInsets.only(right: 8)
                              : EdgeInsets.only(left: 0.0),
                          child: Text(
                            'explore_more_courses',
                            style:
                                Styles.bold(color: Color(0xff0E1638), size: 14),
                          ).tr()),
                      Expanded(child: SizedBox()),
                      TextButton(
                        onPressed: () {
                          menuProvider?.updateCurrentIndex('/g-school');
                        },
                        child: Text('view_all',
                            style: Styles.regular(
                              size: 12,
                              color: ColorConstants.BODY_TEXT,
                            )).tr(),
                      ),
                    ],
                  ),
                ),

                //show courses list
                Container(
                    padding: EdgeInsets.all(10),
                    height: (MediaQuery.of(context).size.height *
                        (Utility().isRTL(context) ? 0.48 : 0.38)),
                    width: MediaQuery.of(context).size.width,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemBuilder: (BuildContext context, int index) {
                        return GetCourseTemplate(
                            context: context,
                            recommendedcourses: recommendedCourseList,
                            yourCourses: recommendedCourseList![index],
                            index: index,
                            tag: 'TagReco',
                            size: MediaQuery.of(context).size.height * 0.35);
                      },
                      itemCount: recommendedCourseList?.length ?? 0,
                      shrinkWrap: true,
                    )),
                SizedBox(
                  height: 20,
                ),
              ],
            );
          });
    return SizedBox();
  }

  renderCarvaanPageView() {
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("dashboard_carvan_limit") == null) {
            return BlankPage();
          } else if (box.get("dashboard_carvan_limit").isEmpty) {
            return SizedBox();
          }
          carvaanList = box
              .get("dashboard_carvan_limit")
              .map((e) =>
                  DashboardCarvanLimit.fromJson(Map<String, dynamic>.from(e)))
              .cast<DashboardCarvanLimit>()
              .toList();

          return Container(
            decoration: BoxDecoration(color: ColorConstants.WHITE),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                        padding: Utility().isRTL(context)
                            ? EdgeInsets.symmetric(
                                vertical: 8,
                                horizontal: 16,
                              )
                            : EdgeInsets.symmetric(
                                vertical: 8,
                                horizontal: 16,
                              ),
                        child: Text(
                          'Recent_community',
                          style: Styles.bold(
                              color: ColorConstants.HEADING_TITLE, size: 14),
                        ).tr()),
                    Expanded(child: SizedBox()),
                    IconButton(
                        onPressed: () {
                          FirebaseAnalytics.instance.logEvent(
                              name: 'Recent_community_dashboard_view_list',
                              parameters: {
                                "latest_reels": 'view all',
                              });
                          menuProvider?.updateCurrentIndex('/g-carvaan');
                        },
                        icon: Icon(
                          Icons.arrow_forward_ios,
                          color: ColorConstants.HEADING_TITLE,
                          size: 20,
                        ))
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Container(
                    height: Utility().isRTL(context)
                        ? height(context) * 0.6
                        : height(context) * 0.55,
                    child: PageView.builder(
                      controller: _pageController,
                      itemCount: carvaanList?.length,
                      onPageChanged: (page) {
                        setState(() {
                          selectedPage = page;
                        });
                      },
                      itemBuilder: (BuildContext context, int index) {
                        final now = currentIndiaTime;

                        var millis =
                            int.parse(carvaanList![index].createdAt.toString());
                        DateTime date = DateTime.fromMillisecondsSinceEpoch(
                          millis * 1000,
                        );
                        List<TextSpan> _getTextSpans(String text) {
                          List<TextSpan> spans = [];
                          RegExp exp = RegExp(r'(#\w+)|([^#]+)');
                          Iterable<RegExpMatch> matches = exp.allMatches(text);

                          for (var match in matches) {
                            String matchText = match[0] ?? '';
                            spans.add(TextSpan(
                              text: matchText,
                              style: TextStyle(
                                color: matchText.startsWith('#')
                                    ? Colors.blue
                                    : Colors.black,
                              ),
                            ));
                          }
                          return spans;
                        }

                        return Container(
                          width: MediaQuery.of(context).size.width * 0.8,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                  color: ColorConstants.DIVIDER_COLOR_1)),
                          margin: EdgeInsets.all(8),
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 8.0,
                                      right: 8.0,
                                      top: 15.0,
                                      bottom: 8.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: <Widget>[
                                      Center(
                                        child: ClipOval(
                                            child: Image.network(
                                          '${carvaanList?[index].profileImage}',
                                          height: 30,
                                          width: 30,
                                          fit: BoxFit.cover,
                                          errorBuilder: (context, url, error) {
                                            return SvgPicture.asset(
                                              'assets/images/default_user.svg',
                                              height: 30,
                                              width: 30,
                                              allowDrawingOutsideViewBox: true,
                                            );
                                          },
                                          loadingBuilder: (BuildContext context,
                                              Widget child,
                                              ImageChunkEvent?
                                                  loadingProgress) {
                                            if (loadingProgress == null)
                                              return child;
                                            return Shimmer.fromColors(
                                              baseColor: Color(0xffe6e4e6),
                                              highlightColor: Color(0xffeaf0f3),
                                              child: Container(
                                                  height: 50,
                                                  margin:
                                                      EdgeInsets.only(left: 2),
                                                  width: 50,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    shape: BoxShape.circle,
                                                  )),
                                            );
                                          },
                                        )),
                                      ),
                                      Expanded(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: <Widget>[
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 8.0,
                                                  right: 8.0,
                                                  top: 2.0),
                                              child: Text(
                                                Utility().decrypted128(
                                                    '${carvaanList?[index].name}'),
                                                style: Styles.textRegular(
                                                    lineHeight: 1, size: 14),
                                                maxLines: 1,
                                              ),
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 8.0, right: 8.0),
                                              child: Text(
                                                Utility()
                                                    .calculateTimeDifferenceBetween(
                                                        DateTime.parse(date
                                                            .toString()
                                                            .substring(0, 19)),
                                                        currentIndiaTime!,
                                                        context),
                                                style: Styles.regular(
                                                  size: 12,
                                                  lineHeight: 1.2,
                                                ),
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 8),
                                  margin: EdgeInsets.only(bottom: 10),
                                  width:
                                      MediaQuery.of(context).size.width * 0.8,
                                  child: CarvaanListItem(
                                      description:
                                          carvaanList?[index].description ??
                                              ''),
                                  //  Text(
                                  //     '${carvaanList?[index].description ?? ''}',
                                  //     maxLines: 2,
                                  //     overflow: TextOverflow.ellipsis,
                                  //     style: Styles.regular(
                                  //       size: 14,
                                  //       lineHeight: 1.2,
                                  //     )),
                                ),
                                carvaanList?[index]
                                                .resourcePath
                                                ?.contains('.mp4') ==
                                            true ||
                                        carvaanList?[index]
                                                .resourcePath
                                                ?.contains('.mov') ==
                                            true
                                    ? Container(
                                        height: 290,
                                        child: Center(
                                          child: VideoPlayerWidget(
                                            videoUrl:
                                                '${carvaanList?[index].resourcePath}',
                                          ),
                                        ))
                                    : Image.network(
                                        '${carvaanList?[index].resourcePath}',
                                        height: 290,
                                        width: double.infinity,
                                        fit: BoxFit.fitWidth),
                                Spacer(),
                                MultiProvider(
                                  providers: [
                                    ChangeNotifierProvider<GCarvaanListModel>(
                                      create: (context) => GCarvaanListModel(
                                          carvaanList
                                              ?.map((e) => GCarvaanPostElement(
                                                  id: e.id,
                                                  commentCount: e.commentCount,
                                                  likeCount: e.likeCount,
                                                  userLiked: e.userLiked,
                                                  programContentId:
                                                      e.programContentId))
                                              .toList()),
                                    ),
                                  ],
                                  child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 5.0,
                                        horizontal: 10.0,
                                      ),
                                      child: Consumer<GCarvaanListModel>(
                                        builder: (context, carvaanListModel,
                                                child) =>
                                            Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          mainAxisSize: MainAxisSize.max,
                                          children: <Widget>[
                                            InkWell(
                                              onTap: () {
                                                setState(() {
                                                  if (carvaanListModel
                                                          .isLiked(index) ==
                                                      true) {
                                                    updateLikeandViews(
                                                        0,
                                                        carvaanListModel
                                                            .list?[index].id);

                                                    carvaanListModel
                                                        .updateIsLiked(
                                                            index, 0);

                                                    carvaanListModel
                                                        .decrementLike(index);
                                                  } else {
                                                    updateLikeandViews(
                                                        1,
                                                        carvaanListModel
                                                            .list?[index].id);

                                                    carvaanListModel
                                                        .updateIsLiked(
                                                            index, 1);

                                                    carvaanListModel
                                                        .incrementLike(index);
                                                  }
                                                });
                                              },
                                              child: Container(
                                                child: Row(
                                                  children: <Widget>[
                                                    Padding(
                                                      padding: Utility()
                                                              .isRTL(context)
                                                          ? EdgeInsets.only(
                                                              left: 4.0,
                                                            )
                                                          : EdgeInsets.only(
                                                              right: 4.0,
                                                            ),
                                                      child: SvgPicture.asset(
                                                        carvaanListModel.isLiked(
                                                                    index) ==
                                                                false
                                                            ? 'assets/images/like_icon.svg'
                                                            : 'assets/images/liked_icon.svg',
                                                        height: 18.8,
                                                        width: 17.86,
                                                        colorFilter: ColorFilter.mode(
                                                            carvaanListModel.isLiked(
                                                                        index) ==
                                                                    false
                                                                ? ColorConstants
                                                                    .HEADING_TITLE
                                                                : ColorConstants()
                                                                    .primaryColor()!,
                                                            BlendMode.srcIn),
                                                      ),
                                                    ),
                                                    Text(
                                                      () {
                                                        int likeCount =
                                                            (carvaanListModel
                                                                    .list?[
                                                                        index]
                                                                    .likeCount ??
                                                                0);

                                                        if (likeCount == 0) {
                                                          return '${tr('like')}';
                                                        }
                                                        if (likeCount == 1) {
                                                          return '$likeCount ${tr('like')}';
                                                        }
                                                        return '$likeCount ${tr('likes')}';
                                                      }(),
                                                      // carvaanListModel
                                                      //             .list?[index]
                                                      //             .likeCount !=
                                                      //         0
                                                      //     ? '${carvaanListModel.list?[index].likeCount} ${tr('like')}'
                                                      //     : tr('like')

                                                      style: Styles.regular(
                                                          size: 12,
                                                          color: ColorConstants
                                                              .BLACK),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            InkWell(
                                              onTap: () {
                                                showModalBottomSheet(
                                                        context: context,
                                                        backgroundColor:
                                                            ColorConstants.WHITE,
                                                        isScrollControlled: true,
                                                        builder: (context) {
                                                          return FractionallySizedBox(
                                                            heightFactor: 0.7,
                                                            child:
                                                                CommentViewPage(
                                                              postId:
                                                                  carvaanList?[
                                                                          index]
                                                                      .id,
                                                              value:
                                                                  carvaanListModel,
                                                            ),
                                                          );
                                                        })
                                                    .then((value) =>
                                                        setState(() {}));
                                              },
                                              child: Container(
                                                child: Row(
                                                  children: <Widget>[
                                                    Padding(
                                                      padding: Utility()
                                                              .isRTL(context)
                                                          ? EdgeInsets.only(
                                                              left: 4.0,
                                                            )
                                                          : EdgeInsets.only(
                                                              right: 4.0,
                                                            ),
                                                      child: SvgPicture.asset(
                                                        'assets/images/comment_icon.svg',
                                                        height: 18.8,
                                                        width: 17.86,
                                                        allowDrawingOutsideViewBox:
                                                            true,
                                                      ),
                                                    ),
                                                    Text(
                                                      () {
                                                        // carvaanListModel
                                                        //           .list?[index]
                                                        //           .commentCount !=
                                                        //       0
                                                        //   ? '${carvaanListModel.list?[index].commentCount} ${tr('comment')}'
                                                        //   : ' ${tr('comment')}'

                                                        int commentCount =
                                                            (carvaanListModel
                                                                    .list?[
                                                                        index]
                                                                    .commentCount ??
                                                                0);

                                                        if (commentCount == 0) {
                                                          return '${tr('comment')}';
                                                        }
                                                        if (commentCount == 1) {
                                                          return '$commentCount ${tr('comment')}';
                                                        }
                                                        return '$commentCount ${tr('comments')}';
                                                      }(),
                                                      style: Styles.regular(
                                                          size: 12,
                                                          color: ColorConstants
                                                              .BLACK),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            InkWell(
                                              onTap: () {
                                                carvaanList![index]
                                                    .description ??= '';

                                                if (['mp4', 'mov'].contains(
                                                    '${'${carvaanList?[index].resourcePath}'}'
                                                        .split('.')
                                                        .last)) {
                                                  Utility.shortLink(
                                                          '${'${carvaanList?[index].resourcePath}'}')
                                                      .then((value) {
                                                    String? s = carvaanList![
                                                                    index]
                                                                .description!
                                                                .length >
                                                            1000
                                                        ? carvaanList![index]
                                                            .description
                                                            ?.substring(0, 1000)
                                                        : '${carvaanList?[index].description}...';
                                                    SharePlus.instance.share(
                                                        ShareParams(
                                                            text:
                                                                "$value ${s ?? ''}"));
                                                  });
                                                } else {
                                                  if (Platform.isAndroid) {
                                                    try {
                                                      Utility().shareFile(
                                                          context,
                                                          url:
                                                              '${'${carvaanList?[index].resourcePath}'}',
                                                          text: carvaanList![
                                                                          index]
                                                                      .description!
                                                                      .length >
                                                                  1000
                                                              ? carvaanList![
                                                                      index]
                                                                  .description
                                                                  ?.substring(
                                                                      0, 1000)
                                                              : '${carvaanList?[index].description}...');
                                                    } catch (e, stackTrace) {
                                                      Log.v(
                                                          "stacktrace $e, $stackTrace");
                                                    }
                                                  } else {
                                                    Utility.shortLink(
                                                            '${'${carvaanList?[index].resourcePath}'}')
                                                        .then((value) {
                                                      String? msg = carvaanList![
                                                                      index]
                                                                  .description!
                                                                  .length >
                                                              1000
                                                          ? carvaanList![index]
                                                              .description
                                                              ?.substring(
                                                                  0, 1000)
                                                          : '${carvaanList?[index].description}...';
                                                      Share.share(
                                                          '$value\n${msg ?? ''}]');
                                                    });
                                                  }
                                                }

                                                // Utility.shortLink(
                                                //         '${carvaanList?[index].resourcePath}')
                                                //     .then((value) {
                                                //   Share.share(value);
                                                //   FirebaseAnalytics.instance
                                                //       .logEvent(
                                                //           name: 'share_url',
                                                //           parameters: {
                                                //         "type":
                                                //             "dashboard_post_share",
                                                //       });
                                                // });
                                              },
                                              child: Container(
                                                child: Row(
                                                  children: <Widget>[
                                                    Padding(
                                                      padding: Utility()
                                                              .isRTL(context)
                                                          ? EdgeInsets.only(
                                                              left: 4.0,
                                                            )
                                                          : EdgeInsets.only(
                                                              right: 4.0,
                                                            ),
                                                      child: SvgPicture.asset(
                                                        'assets/images/share_icon.svg',
                                                        height: 18.8,
                                                        width: 17.86,
                                                        allowDrawingOutsideViewBox:
                                                            true,
                                                      ),
                                                    ),
                                                    Text(
                                                      'share',
                                                      style: Styles.regular(
                                                          size: 12,
                                                          color: ColorConstants
                                                              .BLACK),
                                                    ).tr()
                                                  ],
                                                ),
                                              ),
                                            )
                                          ],
                                        ),
                                      )),
                                ),
                              ]),
                        );
                      },
                    ),
                  ),
                ),
                SizedBox(
                  height: 10.0,
                ),
                _dots(selectedPage, carvaanList?.length as int),
                SizedBox(
                  height: 20.0,
                )
              ],
            ),
          );
        });
  }

  renderTopBanner() {
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("getDashboardIsVisible") == null) {
            return BlankPage();
          } else if (box.get("getDashboardIsVisible").isEmpty) {
            return SizedBox();
          }

          Map<dynamic, dynamic> isBanner =
              new Map<dynamic, dynamic>.from(box.get("getDashboardIsVisible"));

          return isBanner['learner_dashboard_banner'] == '1' &&
                  dashboardContentResponse?.data?.banners?.length != 0
              ? Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (APK_DETAILS["package_name"] ==
                              "com.singulariswow.mec" ||
                          APK_DETAILS["package_name"] ==
                              "com.singularis.mesc" ||
                          APK_DETAILS["package_name"] ==
                              "com.singularis.jumeira" ||
                          APK_DETAILS["package_name"] ==
                              "com.singulariswow.aid") ...{
                        Column(
                          children: [
                            SizedBox(
                              height: 5,
                            ),
                            GestureDetector(
                              onTap: () {
                                String url =
                                    '${dashboardContentResponse?.data?.banners![_current].bannerLink}';
                                if (dashboardContentResponse?.data == null) {
                                  return;
                                }
                                if (dashboardContentResponse
                                        ?.data?.banners![_current].bannerType
                                        ?.toLowerCase() ==
                                    'external') {
                                  if ('$url'.contains('mec.edu.om') ||
                                      '$url'.contains('sis.mec.edu.om') ||
                                      '$url'.contains('mecfuture.mec.edu.om')) {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                TermsAndCondition(
                                                    url: '$url' +
                                                        Preference.getInt(
                                                                Preference
                                                                    .USER_ID)
                                                            .toString(),
                                                    title: ""),
                                            maintainState: false));
                                  } else if ('$url'.contains('mec.edu.om')) {
                                  } else {
                                    launchUrl(Uri.parse('$url'),
                                        mode: LaunchMode.externalApplication);
                                  }
                                } else {
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              CompetitionDetail(
                                                competitionId: int.parse(
                                                    '${dashboardContentResponse?.data?.banners![_current].bannerLink}'),
                                                isEvent: true,
                                              )));
                                }
                                //_launchURL('${APK_DETAILS['module_registration']}'+Preference.getString(Preference.SSOTOKEN).toString());
                              },
                              child: SizedBox(
                                // height: kIsWeb ? 240 : 120,
                                /*child: Padding(
                                  padding: const EdgeInsets.only(
                                      left: 10.0, right: 10.0),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: CachedNetworkImage(
                                      //imageUrl: '${dashboardContentResponse?.data?.bannerUrl}',
                                      imageUrl: '${dashboardContentResponse?.data?.banners![0].bannerUrl}',
                                      height: 200,
                                      width: double.infinity,
                                      errorWidget: (context, url, error) =>
                                          Shimmer.fromColors(
                                        baseColor: ColorConstants.GREY_5,
                                        highlightColor: ColorConstants.DIVIDER,
                                        child: Container(
                                            height: kIsWeb ? 240 : 150,
                                            margin: EdgeInsets.only(left: 2),
                                            width: 70,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                            )),
                                      ),
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                ),*/

                                child: dasboardListLoading == false &&
                                        imageSliders != null
                                    ? Column(
                                        children: [
                                          //TODO:Top Banner
                                          Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: CarouselSlider(
                                              items: imageSliders,
                                              options: CarouselOptions(
                                                //height: 172,
                                                height: 142,
                                                aspectRatio: 2.0,
                                                viewportFraction: 1.0,
                                                initialPage: 0,
                                                enableInfiniteScroll:
                                                    dashboardContentResponse
                                                                ?.data
                                                                ?.banners!
                                                                .length !=
                                                            1
                                                        ? true
                                                        : false,
                                                reverse: false,
                                                autoPlay:
                                                    dashboardContentResponse
                                                                ?.data
                                                                ?.banners!
                                                                .length !=
                                                            1
                                                        ? true
                                                        : false,
                                                enlargeFactor: 0.3,
                                                autoPlayInterval:
                                                    Duration(seconds: 3),
                                                autoPlayAnimationDuration:
                                                    Duration(milliseconds: 800),
                                                autoPlayCurve:
                                                    Curves.fastOutSlowIn,
                                                enlargeCenterPage: true,
                                                scrollDirection:
                                                    Axis.horizontal,
                                                onPageChanged: (index, reason) {
                                                  setState(() {
                                                    _current = index;
                                                  });
                                                },
                                              ),
                                            ),
                                          ),
                                          dashboardContentResponse
                                                      ?.data?.banners!.length !=
                                                  1
                                              ? Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children:
                                                      dashboardContentResponse!
                                                          .data!.banners!
                                                          .asMap()
                                                          .entries
                                                          .map((entry) {
                                                    return GestureDetector(
                                                      onTap: () => _controller
                                                          .animateToPage(
                                                              entry.key),
                                                      child: Container(
                                                        width: _current ==
                                                                entry.key
                                                            ? 8.0
                                                            : 4.0,
                                                        height: _current ==
                                                                entry.key
                                                            ? 8.0
                                                            : 4.0,
                                                        margin: EdgeInsets
                                                            .symmetric(
                                                                vertical: 5.0,
                                                                horizontal:
                                                                    2.0),
                                                        decoration: BoxDecoration(
                                                            shape:
                                                                BoxShape.circle,
                                                            color: (Theme.of(context)
                                                                            .brightness ==
                                                                        Brightness
                                                                            .dark
                                                                    ? Colors
                                                                        .white
                                                                    : ColorConstants
                                                                        .PRIMARY_COLOR)
                                                                .withValues(
                                                                    alpha: _current ==
                                                                            entry.key
                                                                        ? 0.9
                                                                        : 0.4)),
                                                      ),
                                                    );
                                                  }).toList(),
                                                )
                                              : SizedBox(),
                                        ],
                                      )
                                    : SizedBox(
                                        child: Shimmer.fromColors(
                                          baseColor: ColorConstants.GREY_5,
                                          highlightColor:
                                              ColorConstants.DIVIDER,
                                          child: Container(
                                              height: kIsWeb ? 240 : 150,
                                              margin: EdgeInsets.only(left: 2),
                                              width: double.infinity,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                              )),
                                        ),
                                      ),
                              ),
                            ),
                          ],
                        )
                      },
                    ],
                  ),
                )
              : SizedBox();
        });
  }

  renderSkillData() {
    return Container(
      decoration: BoxDecoration(color: ColorConstants.WHITE),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                  padding: Utility().isRTL(context)
                      ? EdgeInsets.only(right: 15.0)
                      : EdgeInsets.only(left: 15.0),
                  child: Icon(Icons.trending_up_outlined, size: 18)),
              Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 10,
                  ),
                  child: Text(
                    'gain_skill',
                    style: Styles.bold(
                        size: 14, color: ColorConstants.HEADING_PRIMARY_COLOR),
                  ).tr()),
            ],
          ),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 3),
            child: Container(
              height: 45,
              child: ListView.builder(
                itemCount: dashboardContentResponse
                        ?.data?.userSkillAssessment?.length ??
                    0,
                scrollDirection: Axis.horizontal,
                itemBuilder: (BuildContext context, int index) {
                  Map<String, UserSkillAssessment>? userSkillAssessmentMap =
                      dashboardContentResponse?.data?.userSkillAssessment;

                  if (userSkillAssessmentMap != null) {
                    final userSkillAssessmentList =
                        userSkillAssessmentMap.values.toList();
                    UserSkillAssessment userSkillAssessment =
                        userSkillAssessmentList[index];
                    String categoryName =
                        userSkillAssessment.categoryName ?? 'Unknown Category';
                    List<Skill> skills = userSkillAssessment.skills;

                    return InkWell(
                      onTap: () {
                        // When a category is clicked, update the selected category name and skills
                        setState(() {
                          selectedCategoryName = categoryName;
                          selectedSkills = skills;
                          skillTabSelection = index;
                        });
                      },
                      child: Container(
                        width:
                            min(MediaQuery.of(context).size.width, 280) * 0.4,
                        decoration: BoxDecoration(
                          color: skillTabSelection == index
                              ? ColorConstants.PRIMARY_COLOR
                              : Colors.blue.withValues(alpha: 0.08),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        margin: EdgeInsets.all(4),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Center(
                            child: Text(
                              categoryName,
                              style: TextStyle(
                                color: skillTabSelection == index
                                    ? Colors.white
                                    : Colors.black,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                              softWrap: true,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),
                      ),
                    );
                  }

                  // Return an empty container if the map is null
                  return Container();
                },
              ),
            ),
          ),

          Divider(),

          // Skills List (Vertical ListView.builder)
          Container(
            //height: 300,
            child: selectedSkills != null
                ? ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: selectedSkills!.length,
                    itemBuilder: (BuildContext context, int skillIndex) {
                      Skill skill = selectedSkills![skillIndex];

                      return SkillChildCard(
                        skillId: skill.skillId,
                        skill: skill.name,
                        level: skill.userWeightageLabel,
                        weightagePerNo: skill.userWeightagePerNo,
                      );
                    },
                  )
                : Center(
                    child: Text(
                      selectedCategoryName != null
                          ? tr('no_skills_available') + ' $selectedCategoryName'
                          : tr('select_category_view_skill'),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  renderFutureTrends() {
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("getDashboardIsVisible") == null) {
            return BlankPage();
          } else if (box.get("getDashboardIsVisible").isEmpty) {
            return SizedBox();
          }

          Map<dynamic, dynamic> data =
              new Map<dynamic, dynamic>.from(box.get("getDashboardIsVisible"));

          return Preference.getString(Preference.ROLE) == 'Learner'
              ? data['learner_dashboard_futuretrends'] == '1'
                  ? Container(
                      margin: data['learner_dashboard_banner'] == '1'
                          ? EdgeInsets.only(top: 10)
                          : EdgeInsets.only(top: 10),
                      child: dasboardListLoading == false
                          ? FutureTrendsList(
                              domainList:
                                  dashboardContentResponse?.data?.futureTrends,
                            )
                          : FutureTrendBlankPage())
                  : SizedBox()
              : SizedBox();
        });
  }

  renderInterestArea() {
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("getDashboardIsVisible") == null) {
            return BlankPage();
          } else if (box.get("getDashboardIsVisible").isEmpty) {
            return SizedBox();
          }

          Map<dynamic, dynamic> data =
              new Map<dynamic, dynamic>.from(box.get("getDashboardIsVisible"));

          return Preference.getString(Preference.ROLE) == 'Learner'
              ? data['learner_dashboard_interestarea'] == '1'
                  ? Container(
                      child: dashboardContentResponse?.data?.interestArea !=
                              null
                          ? InterestAreaPage(
                              interestArea:
                                  dashboardContentResponse?.data?.interestArea,
                            )
                          : FutureTrendBlankPage())
                  : SizedBox()
              : SizedBox();
        });
  }

  renderIndustryDomain() {
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("getDashboardIsVisible") == null) {
            return BlankPage();
          } else if (box.get("getDashboardIsVisible").isEmpty) {
            return SizedBox();
          }

          Map<dynamic, dynamic> data =
              new Map<dynamic, dynamic>.from(box.get("getDashboardIsVisible"));

          return Preference.getString(Preference.ROLE) == 'Learner'
              ? data['learner_dashboard_jobdomains'] == '1'
                  ? Container(
                      child: domainLoading == false
                          ? IndustryDomainPage(
                              jobDomain:
                                  dashboardContentResponse?.data?.jobDomain,
                            )
                          : SizedBox())
                  : SizedBox()
              : SizedBox();
        });
  }

  renderJobInternship() {
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("getDashboardIsVisible") == null) {
            return BlankPage();
          } else if (box.get("getDashboardIsVisible").isEmpty) {
            return SizedBox();
          }

          Map<dynamic, dynamic> data =
              new Map<dynamic, dynamic>.from(box.get("getDashboardIsVisible"));

          return data['learner_dashboard_internship'] == '1'
              ? Container(
                  child: Column(
                  children: [
                    if (Preference.getString(Preference.ROLE) == 'Learner' ||
                        Preference.getString(Preference.ROLE) == 'Alumni' ||
                        Preference.getString(Preference.ROLE) == 'Lead') ...[
                      SizedBox(height: matchingJobsResp?.length != 0 ? 10 : 0),
                      /*height: featuredInternshipsResponse?.data?.length != 0
                              ? 10
                              : 0),
                      featuredInternshipsLoading == false &&
                              featuredInternshipsResponse?.data?.length != 0 &&
                              featuredInternshipsResponse?.data != null
                          ? featuredJobsInternships()
                          : featuredInternshipsLoading == false
                              ? SizedBox()
                              : JobBlankPage(),*/
                      matchingJobsResp?.length != 0 && matchingJobsResp != null
                          ? invitedJobsCard()
                          : dasboardListLoading == false
                              ? SizedBox()
                              : JobBlankPage(),
                    ],
                  ],
                ))
              : SizedBox();
        });
  }

  renderCompetitions() {
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("getDashboardIsVisible") == null) {
            return BlankPage();
          } else if (box.get("getDashboardIsVisible").isEmpty) {
            return SizedBox();
          }

          Map<dynamic, dynamic> data =
              new Map<dynamic, dynamic>.from(box.get("getDashboardIsVisible"));

          return data['learner_dashboard_competetion'] == '1'
              ? Container(
                  child: competitionResponse?.event != null &&
                          competitionResponse?.event?.length != 0
                      ? competitionsWidgets()
                      : SizedBox()) /*Container(
                  child: Preference.getString(Preference.ROLE)?.toLowerCase() == 'learner' || Preference.getString(Preference.ROLE)?.toLowerCase() == 'lead'
                      ? competitionsWidgets()
                      : SizedBox())*/
              : SizedBox();
        });
  }

  renderProtfolio() {
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("getDashboardIsVisible") == null) {
            return BlankPage();
          } else if (box.get("getDashboardIsVisible").isEmpty) {
            return SizedBox();
          }
          Map<dynamic, dynamic> data =
              new Map<dynamic, dynamic>.from(box.get("getDashboardIsVisible"));

          return data['learner_dashboard_portfolio'] == '1'
              ? Container(
                  child: Preference.getString(Preference.ROLE) == 'Learner'
                      ? BuildYourPortfolioCard(
                          context: context,
                          menuProvider: menuProvider,
                          colorBg: ColorConstants.ORANGE,
                          strTitle: tr('build_portfolio'),
                          strDes: tr('build_portfolio_text'),
                          clickType: 'build_portfolio')
                      : SizedBox(),
                )
              : SizedBox();
        });
  }

  renderMatchingJobProfile() {
    return ValueListenableBuilder(
        valueListenable: Hive.box(DB.CONTENT).listenable(),
        builder: (bc, Box box, child) {
          if (box.get("getDashboardIsVisible") == null) {
            return BlankPage();
          } else if (box.get("getDashboardIsVisible").isEmpty) {
            return SizedBox();
          }
          Map<dynamic, dynamic> data =
              new Map<dynamic, dynamic>.from(box.get("getDashboardIsVisible"));

          Preference.setString(Preference.LEARNERDASHBOARDJOBPORTFOLIO,
              '${data['learner_dashboard_jobportfolio']}');

          return data['learner_dashboard_jobportfolio'] == '1'
              ? Container(
                  child: Preference.getString(Preference.ROLE) == 'Learner'
                      ? Container(
                          margin: EdgeInsets.only(top: 0),
                          color: Colors.white,
                          child: Padding(
                            padding: const EdgeInsets.all(15.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                        '${Preference.getInt(Preference.RESUME_PARSER_DATA_COUNT) ?? 0}${Preference.getInt(Preference.RESUME_PARSER_DATA_COUNT) != 0 && Preference.getInt(Preference.RESUME_PARSER_DATA_COUNT) != null ? '+' : ''} ',
                                        style: Styles.bold(
                                            color: ColorConstants.BLACK,
                                            size: 20)),
                                    Text('jobs',
                                            style: Styles.bold(
                                                color: ColorConstants.BLACK,
                                                size: 20))
                                        .tr(),
                                    Padding(
                                      padding: const EdgeInsets.only(left: 8.0),
                                      child: Text('matching_your_profile',
                                              style: Styles.textRegular(
                                                  color: ColorConstants.BLACK,
                                                  size: 16))
                                          .tr(),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Text.rich(
                                    TextSpan(
                                      children: [
                                        TextSpan(
                                            text: '${tr('profile_complete')}: ',
                                            style: Styles.regular(
                                                color: ColorConstants.BLACK,
                                                size: 14)),
                                        TextSpan(
                                          text:
                                              '${Preference.getInt(Preference.PROFILE_PERCENT) ?? 0}%',
                                          style: Styles.bold(
                                              color: ColorConstants.BLACK),
                                        ),
                                      ],
                                    ),
                                    textAlign: TextAlign.start),
                                Container(
                                  height: 5,
                                  width: MediaQuery.of(context).size.width,
                                  margin: EdgeInsets.only(top: 8.0),
                                  decoration: BoxDecoration(
                                      color: ColorConstants.BLACK
                                          .withValues(alpha: 0.2),
                                      borderRadius: BorderRadius.circular(10)),
                                  child: Stack(
                                    children: [
                                      Container(
                                        height: 10,
                                        width:
                                            MediaQuery.of(context).size.width *
                                                ((Preference.getInt(Preference
                                                            .PROFILE_PERCENT) ??
                                                        0) /
                                                    100),
                                        decoration: BoxDecoration(
                                            color: Colors.green,
                                            borderRadius:
                                                BorderRadius.circular(10)),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 12,
                                ),
                                Row(
                                  children: [
                                    ElevatedButton(
                                      style: ButtonStyle(
                                        backgroundColor:
                                            WidgetStateProperty.all(
                                                ColorConstants.PRIMARY_COLOR),
                                        shape: WidgetStateProperty.all(
                                            RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(30))),
                                      ),
                                      child: Row(
                                        children: [
                                          (Preference.getInt(Preference
                                                          .PROFILE_PERCENT) ??
                                                      0) !=
                                                  100
                                              ? Text('complete_your_profile')
                                                  .tr()
                                              : Text('update_your_profile')
                                                  .tr(),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 2.0),
                                            child: Icon(
                                              Icons.arrow_forward_ios_rounded,
                                              size: 18,
                                            ),
                                          ),
                                        ],
                                      ),
                                      onPressed: () {
                                        Navigator.push(
                                                context,
                                                NextPageRoute(
                                                    NewPortfolioPage()))
                                            .then((value) {
                                          if (value != null)
                                            menuProvider
                                                ?.updateCurrentIndex(value);
                                        });
                                      },
                                    ),
                                    Spacer(),
                                    InkWell(
                                      onTap: () {
                                        /*Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => TermsAndCondition(
                                      url: '${APK_DETAILS['explore-jobs-webview_url']}' +
                                          Preference.getInt(
                                              Preference.USER_ID)
                                              .toString(),
                                      title: tr('explore_jobs'),
                                    ),
                                    maintainState: false));*/
                                        Navigator.push(
                                            context,
                                            NextPageRoute(
                                                ExploreJobListPage(
                                                  indexNo: null,
                                                ),
                                                isMaintainState: true));
                                      },
                                      child: Row(
                                        children: [
                                          Text('explore_jobs',
                                                  style: Styles.bold(
                                                      color: ColorConstants
                                                          .PRIMARY_COLOR,
                                                      size: 15))
                                              .tr(),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 2.0),
                                            child: Icon(
                                                Icons.arrow_forward_ios_rounded,
                                                size: 18,
                                                color: ColorConstants
                                                    .PRIMARY_COLOR),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        )
                      : SizedBox(),
                )
              : SizedBox();
        });
  }

  void updateLikeandViews(int? like, contentId) async {
    BlocProvider.of<HomeBloc>(context).add(
        LikeContentEvent(contentId: contentId, like: like, type: 'contents'));
  }

  _dots(int index, int postCount) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        DotsIndicator(
          dotsCount: postCount,
          position: index.toDouble(),
          decorator: DotsDecorator(
            size: const Size.square(8.0),
            color: Color(0xffCCCACA),
            spacing: const EdgeInsets.only(left: 5.0),
            activeColor: ColorConstants().gradientLeft(),
            activeSize: const Size(22.0, 8.0),
            activeShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0)),
          ),
        ),
      ],
    );
  }

  renderFeaturedContentLimit() {
    return ValueListenableBuilder(
      valueListenable: Hive.box(DB.CONTENT).listenable(),
      builder: (bc, Box box, child) {
        if (box.get("dashboard_featured_content_limit") == null) {
          return BlankPage();
        } else if (box.get("dashboard_featured_content_limit").isEmpty) {
          return SizedBox();
        }

        featuredContentList = box
            .get("dashboard_featured_content_limit")
            .map((e) => DashboardFeaturedContentLimit.fromJson(
                Map<String, dynamic>.from(e)))
            .cast<DashboardFeaturedContentLimit>()
            .toList();

        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(6)),
            color: ColorConstants.ACCENT_COLOR,
          ),
          child: Column(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    APK_DETAILS['package_name'] == 'com.singularis.mesc'
                        ? SvgPicture.asset(
                            'assets/images/mesc_wow_studio_gradient.svg')
                        : APK_DETAILS['package_name'] ==
                                'com.singularis.jumeira'
                            ? SvgPicture.asset(
                                'assets/images/jumeira_wow_studio_gradient.svg')
                            : SvgPicture.asset(
                                'assets/images/wow_studio_gradient.svg'),
                    SizedBox(width: 8),
                    Text(
                            APK_DETAILS['package_name'] == 'com.singularis.mesc'
                                ? 'mesc_studio'
                                : 'wow_studio',
                            style: Styles.bold(
                                color: ColorConstants.HEADING_TITLE))
                        .tr(),
                    Expanded(child: SizedBox()),
                    InkWell(
                      onTap: () {
                        FirebaseAnalytics.instance.logEvent(
                            name: 'wow_studio_dashboard',
                            parameters: {
                              "wow_studio": 'view all',
                            });

                        Navigator.push(context, NextPageRoute(WowStudio()));
                      },
                      child: Text('view_all',
                          style: Styles.regular(
                            size: 12,
                            color: ColorConstants.BODY_TEXT,
                          )).tr(),
                    ),
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(left: 14, right: 14),
                child: Visibility(
                  visible: featuredContentList!.length > 0,
                  child: GridView.builder(
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: featuredContentList!.length,
                    shrinkWrap: true,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        mainAxisSpacing: 0,
                        crossAxisSpacing: 20,
                        childAspectRatio: 2 / 3,
                        mainAxisExtent:
                            MediaQuery.of(context).size.height * 0.318,
                        crossAxisCount: width(context) > 700 ? 3 : 2),
                    itemBuilder: (BuildContext context, int index) {
                      return InkWell(
                        onTap: () async {
                          FirebaseAnalytics.instance.logEvent(
                              name: 'wow_studio_dashboard',
                              parameters: {
                                "wow_studio":
                                    featuredContentList![index].title ?? '',
                              });
                          showModalBottomSheet(
                              context: context,
                              backgroundColor: ColorConstants.WHITE,
                              isScrollControlled: true,
                              builder: (context) {
                                return FractionallySizedBox(
                                  heightFactor: 1.0,
                                  child: ViewWidgetDetailsPage(
                                    currentID: featuredContentList![index].id,
                                    root: 'dashboard',
                                  ),
                                );
                              });
                        },
                        child: Column(
                          children: [
                            Container(
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12)),
                                child: Stack(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(12),
                                      child: Container(
                                          height: MediaQuery.of(context)
                                                  .size
                                                  .height *
                                              0.25,
                                          width:
                                              MediaQuery.of(context).size.width,
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(12)),
                                          foregroundDecoration: BoxDecoration(
                                              gradient: LinearGradient(
                                            end: const Alignment(0.0, -1),
                                            begin: const Alignment(0.0, 0.8),
                                            colors: [
                                              const Color(0x8A000000)
                                                  .withValues(alpha: 0.4),
                                              Colors.black12
                                                  .withValues(alpha: 0.0)
                                            ],
                                          )),
                                          child: CachedNetworkImage(
                                            imageUrl:
                                                '${featuredContentList![index].resourcePathThumbnail}',
                                            imageBuilder:
                                                (context, imageProvider) =>
                                                    Container(
                                              decoration: BoxDecoration(
                                                  image: DecorationImage(
                                                image: imageProvider,
                                                //fit: BoxFit.fill,
                                                fit: BoxFit.fitHeight,
                                              )),
                                            ),
                                            placeholder: (context, url) =>
                                                Image.asset(
                                              'assets/images/placeholder.png',
                                              fit: BoxFit.fill,
                                            ),
                                            errorWidget:
                                                (context, url, error) =>
                                                    Image.asset(
                                              'assets/images/placeholder.png',
                                              fit: BoxFit.fill,
                                            ),
                                          )),
                                    ),
                                    if (featuredContentList![index]
                                            .resourcePath !=
                                        null)
                                      if (featuredContentList![index]
                                          .resourcePath!
                                          .contains('.mp4'))
                                        Positioned.fill(
                                          child: Align(
                                            alignment: Alignment.center,
                                            child: SvgPicture.asset(
                                              'assets/images/play_video_icon.svg',
                                              height: 30.0,
                                              width: 30.0,
                                              allowDrawingOutsideViewBox: true,
                                            ),
                                          ),
                                        ),
                                  ],
                                )),
                            Container(
                              height: 50,
                              width: width(context),
                              margin: EdgeInsets.only(top: 4),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: 160,
                                    child: Text(
                                        featuredContentList![index].title ?? '',
                                        maxLines: 2,
                                        softWrap: true,
                                        overflow: TextOverflow.ellipsis,
                                        style: Styles.semibold(
                                            size: 14,
                                            color: ColorConstants
                                                .SUB_HEADING_TITLE)),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> getDashboardIsVisible() async {
    box = Hive.box(DB.CONTENT);
    BlocProvider.of<HomeBloc>(context).add(DashboardIsVisibleEvent());
  }

  Future<void> getDasboardList() async {
    box = Hive.box(DB.CONTENT);
    context.read<DashboardContentCubit>().getDashboardContent();
  }

  void handleDashboardIsVisible(DashboardIsVisibleState state) {
    var dashboardIsVisibleState = state;
    setState(() {
      switch (dashboardIsVisibleState.apiState) {
        case ApiStatus.LOADING:
          dashboardIsVisibleLoading = true;
          break;
        case ApiStatus.SUCCESS:
          dashboardViewResponse = state.response;

          dashboardIsVisibleLoading = false;
          break;
        case ApiStatus.ERROR:
          dashboardIsVisibleLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void handleDasboardList(DashboardContentState state) {
    var dashboardContentState = state;
    if (state is DashboardContentLoading) {
      dasboardListLoading = true;
    } else if (state is DashboardContentLoaded) {
      Log.v("DashboardContentState....................");
      Log.v(state.response.data);
      dashboardContentResponse = state.response;
      matchingJobsResp = dashboardContentResponse?.data?.matchingJobs;
      Log.v("DashboardContentState....................");

      Preference.setInt(Preference.ENABLE_MECAT,
          int.parse('${dashboardContentResponse?.data?.enableMecat}'));
      //Preference.setString(Preference.MEC_LEARN, '${dashboardContentResponse?.data?.learnUrl}');
      Preference.setString(Preference.SETUP_GOAL,
          '${dashboardContentResponse?.data?.jobRoleSelected}');

      Preference.setString(Preference.SETUP_GOAL_INTEREST_ID,
          '${dashboardContentResponse?.data?.jobRoleId}');

      imageSliders = dashboardContentResponse?.data?.banners?.map((item) {
            return CachedNetworkImage(
              //imageUrl: '${dashboardContentResponse?.data?.bannerUrl}',
              imageUrl: item.bannerUrl.toString(),
              height: kIsWeb ? 240 : 150,
              width: double.infinity,
              errorWidget: (context, url, error) => Shimmer.fromColors(
                baseColor: ColorConstants.GREY_5,
                highlightColor: ColorConstants.DIVIDER,
                child: Container(
                    height: kIsWeb ? 240 : 150,
                    margin: EdgeInsets.only(left: 2),
                    width: 70,
                    decoration: BoxDecoration(
                      color: Colors.white,
                    )),
              ),
              fit: BoxFit.fill,
            );
          }).toList() ??
          [];

      final userSkillAssessmentMap =
          dashboardContentResponse?.data?.userSkillAssessment;
      if (userSkillAssessmentMap != null && userSkillAssessmentMap.isNotEmpty) {
        final firstCategory = userSkillAssessmentMap.values.first;
        setState(() {
          selectedCategoryName = firstCategory.categoryName;
          selectedSkills = firstCategory.skills;
        });
      }

      /*Map<String, UserSkillAssessment>? userSkillAssessmentMap = dashboardContentResponse?.data?.userSkillAssessment;
          if (userSkillAssessmentMap != null) {

            userSkillAssessmentMap.forEach((key, userSkillAssessment) {
              print('Category Name: ${userSkillAssessment.categoryId}');
              print('Category Name: ${userSkillAssessment.categoryName}');

              userSkillAssessment.skills.forEach((skill) {
                print('Skill Name: ${skill.name}');
              });
            });
          }*/

      dasboardListLoading = false;
    } else if (state is DashboardContentError) {
      dasboardListLoading = false;
    }
    setState(() {});
  }
}

extension on String {
  String capital() {
    return this[0].toUpperCase() + this.substring(1);
  }
}
