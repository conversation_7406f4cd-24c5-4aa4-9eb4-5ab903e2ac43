import 'dart:developer';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/home_response/competition_content_list_resp.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/video_resume/preview_sample_resume_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:open_filex/open_filex.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:path_provider/path_provider.dart';

class EventParticipationCertificate extends StatefulWidget {
  final int? competitionId;

  const EventParticipationCertificate({
    super.key,
    this.competitionId,
  });

  @override
  State<EventParticipationCertificate> createState() =>
      _EventParticipationCertificateState();
}

class _EventParticipationCertificateState
    extends State<EventParticipationCertificate> {
  bool isLoading = false;
  CompetitionContentListResponse? eventCertificate;

  final ReceivePort _port = ReceivePort();
  @override
  void initState() {
    getEventCertificate(competitionId: widget.competitionId);
    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) {
      // String id = data[0];
      // DownloadTaskStatus status = data[1];
      // int progress = data[2];
      setState(() {});
    });

    FlutterDownloader.registerCallback(downloadCallback);

    super.initState();
  }

  static void downloadCallback(String id, int status, int progress) {
    final SendPort send =
        IsolateNameServer.lookupPortByName('downloader_send_port')!;
    send.send([id, status, progress]);
  }

  void getEventCertificate({int? competitionId}) {
    BlocProvider.of<HomeBloc>(context)
        .add(CompetitionContentListEvent(competitionId: competitionId));
  }

  String decodeUrl(String url) {
    // Use Uri.decodeFull to decode the URL
    return Uri.decodeFull(url);
  }

  bool webLoading = true;
  @override
  Widget build(BuildContext context) {
    return BlocManager(
        initState: (context) {},
        child: BlocListener<HomeBloc, HomeState>(
            listener: (context, state) {
              if (state is AppJobListCompeState) {
                _handlecompetitionDetails(state);
              }
            },
            child: Scaffold(
                appBar: AppBar(
                    elevation: 1,
                    leading: BackButton(color: ColorConstants.BLACK),
                    iconTheme: const IconThemeData(color: ColorConstants.WHITE),
                    backgroundColor: ColorConstants.WHITE,
                    title: Text('event_certificates',
                            style: Styles.bold(color: ColorConstants.BLACK))
                        .tr()),
                backgroundColor: Colors.white,
                body: Column(
                  children: [
                    SizedBox(
                        //height: 350,
                        child:
                            getCertificateList(certificate: eventCertificate))

                    // Text('${ Uri.parse(
                    //         '${eventCertificate?.data?.certificate?.htmlUrl}')}')
                  ],
                ))));

    //  ScreenWithLoader(
    //     isLoading: isLoading,
    //     body: eventCertificate != null
    //         ? Column(
    //             children: [
    //               if (eventCertificate != null)
    //                 SizedBox(
    //                     height: 200,
    //                     child: getCertificateList(
    //                         certificate: eventCertificate)),
    //             ],
    //           )
    //         : const SizedBox()))));
  }

  Widget getCertificateList({CompetitionContentListResponse? certificate}) {
    String url =
        '${Uri.encodeFull('${eventCertificate?.data?.certificate?.htmlUrl}')}';

    return url.isNotEmpty
        ? Container(
            color: ColorConstants.WHITE,
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            width: width(context),
            height: 300,
            child: Container(
              decoration: BoxDecoration(
                color: ColorConstants.WHITE,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.5),
                    spreadRadius: 5,
                    blurRadius: 7,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  if (isLoading == false)
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 0,
                      top: 0,
                      child: InAppWebView(
                          onLoadStart: ((controller, url) => setState(() {
                                webLoading = true;
                              })),
                          onLoadStop: ((controller, url) =>
                              Future.delayed(const Duration(seconds: 1))
                                  .then((value) => setState(() {
                                        webLoading = false;
                                      }))),
                          initialOptions: InAppWebViewGroupOptions(
                              crossPlatform: InAppWebViewOptions(
                                mediaPlaybackRequiresUserGesture: true,
                                useShouldOverrideUrlLoading: true,
                              ),
                              ios: IOSInAppWebViewOptions(
                                  allowsInlineMediaPlayback: true,
                                  allowsLinkPreview: true)),
                          //initialUrlRequest: URLRequest(url: Uri.parse('https://edulystventures-my.sharepoint.com/:v:/p/rohit_devadasan/EYOjXzjgVmxEv1tdMYUa25oBj4LPLw0-1s7tHrjbHUR6yQ?nav=eyJyZWZlcnJhbEluZm8iOnsicmVmZXJyYWxBcHAiOiJPbmVEcml2ZUZvckJ1c2luZXNzIiwicmVmZXJyYWxBcHBQbGF0Zm9ybSI6IldlYiIsInJlZmVycmFsTW9kZSI6InZpZXciLCJyZWZlcnJhbFZpZXciOiJNeUZpbGVzTGlua0NvcHkifX0&e=MZvFjd'))),
                          initialUrlRequest: URLRequest(url: WebUri('$url'))),
                    ),
                  if (webLoading)
                    SizedBox(
                      width: double.infinity,
                      height: double.infinity,
                      child: Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        enabled: true,
                        child: Container(
                          width: 200,
                          height: 12,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  Positioned(
                    bottom: 0,
                    child: Container(
                      width: width(context) * 0.92,
                      height: 40,
                      decoration: const ShapeDecoration(
                        color: ColorConstants.PRIMARY_BLUE,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(8),
                            bottomRight: Radius.circular(8),
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          const SizedBox(
                            width: 20,
                          ),
                          SizedBox(
                              width: 24,
                              height: 24,
                              child: Icon(
                                Icons.picture_as_pdf_outlined,
                                size: 30,
                                color: ColorConstants.WHITE,
                              )
                              // SvgPicture.asset(
                              //   'assets/images/pdf.svg',
                              //   color: Colors.white,
                              //   width: 50,
                              // ),
                              ),
                          const SizedBox(
                            width: 10,
                          ),
                          Text(
                            '${''}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Spacer(),
                          InkWell(
                              onTap: () async {
                                log('pdf url is ${eventCertificate?.data?.certificate?.pdfUrl}');
                                if (eventCertificate
                                        ?.data?.certificate?.pdfUrl !=
                                    '') {
                                  Navigator.push(
                                      context,
                                      NextPageRoute(PreviewSampleResume(
                                          title: tr('event_certificates'),
                                          previewUrl:
                                              '${eventCertificate?.data?.certificate?.pdfUrl}',
                                          msg: tr('certificate_not_found'))));
                                } else {
                                  final String url1 =
                                      '${eventCertificate?.data?.certificate?.certificateWebView}';
                                  if (await canLaunchUrl(Uri.parse(url1))) {
                                    await launchUrl(Uri.parse(url1));
                                    ;
                                  } else {
                                    throw 'Could not launch $url1';
                                  }
                                }
                              },
                              child: Icon(Icons.visibility,
                                  color: ColorConstants.WHITE)),
                          /*SizedBox(width: 10),
                          InkWell(
                            onTap: () async{
                              if (eventCertificate?.data?.certificate?.pdfUrl ==
                                  null) {
                                ScaffoldMessenger.of(context)
                                    .showSnackBar(SnackBar(
                                  content: Text('certificate_not_found').tr(),
                                ));
                              } else {
                                certificateDownload(
                                    '${eventCertificate?.data?.certificate?.pdfUrl}');
                              }
                            },
                            child: Container(
                                width: 30,
                                height: 30,
                                decoration: ShapeDecoration(
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(2)),
                                ),
                                child: Icon(
                                  Icons.download,
                                  color: ColorConstants.WHITE,
                                )
                                // Image.asset(
                                //   'assets/images/download.png',
                                //   color: ColorConstants.WHITE,
                                // ),
                                ),
                          ),*/

                          const SizedBox(
                            width: 10,
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )
        : Padding(
            padding: const EdgeInsets.only(top: 300.0),
            child: Center(
                child: Text('certificate_not_found',
                        style: Styles.bold(color: ColorConstants.BLACK))
                    .tr()),
          );
  }

  void _handlecompetitionDetails(AppJobListCompeState state) {
    var competitionState = state;
    setState(() {
      switch (competitionState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................event certificate");
          isLoading = true;
          break;
        case ApiStatus.SUCCESS:
          Log.v(
              "success....................event certificate ${Uri.parse('decode url is ${eventCertificate?.data?.certificate?.htmlUrl}')}");
          eventCertificate = state.response;

          isLoading = false;

          break;
        case ApiStatus.ERROR:
          Log.v("Error..........................event certificate");
          isLoading = false;
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void shareUrl(String url) {
    if (url.isNotEmpty) {
      SharePlus.instance.share(ShareParams(text: url, subject: 'Sharing URL'));
    }
  }

  Future<void> certificateDownload(String url) async {
    DeviceInfoPlugin plugin = DeviceInfoPlugin();
    late AndroidDeviceInfo android;
    try {
      android = await plugin.androidInfo;
    } catch (e) {
      Log.v("exception file download $e");
    }
    // return;
    String localPath;

    final status = await Permission.storage.request();
    if (Platform.isIOS ||
        status.isGranted ||
        android.version.sdkInt >= 33 ||
        await Permission.storage.request().isGranted) {
      //  final externalDir = await getExternalStorageDirectory();
      final status = await Permission.storage.status;

      if (Platform.isAndroid) {
        localPath = "/sdcard/download/";
      } else {
        localPath = (await getApplicationDocumentsDirectory()).path;
      }
      final file = File("$localPath/${url.split('/').last}");
      if (!file.existsSync()) {
        // ignore: use_build_context_synchronously
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
          content: Text('Downloading Start'),
        ));

        final id = await FlutterDownloader.enqueue(
          url: url,
          savedDir: localPath,
          showNotification: true,
          openFileFromNotification: true,
        ).then((value) async {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Successfully Downloaded'),
          ));

          OpenFilex.open("$localPath/${url.split('/').last}");
        });
      } else {
        Utility.showSnackBar(scaffoldContext: context, message: 'file exists');
        OpenFilex.open("$localPath/${url.split('/').last}");
      }
    } else {
      launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      Log.v('Permission Denied');
    }
    return;
  }
}
